{"private": true, "scripts": {"dev": "vite --host 0.0.0.0 --port 5173", "dev:watch": "vite --host 0.0.0.0 --port 5173 --watch", "build": "vite build", "serve": "vite preview", "test": "eslint ./resources/scripts --ext .js,.vue"}, "devDependencies": {"@rvxlab/tailwind-plugin-ios-full-height": "^1.0.0", "@tailwindcss/aspect-ratio": "^0.4.0", "@tailwindcss/forms": "^0.4.0", "@tailwindcss/typography": "^0.5.0", "@vitejs/plugin-vue": "^1.10.0", "@vue/compiler-sfc": "^3.2.22", "autoprefixer": "^10.4.0", "cross-env": "^5.1", "eslint": "^7.27.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-vue": "^7.0.0-beta.4", "laravel-vite": "^0.0.7", "postcss": "^8.4.5", "prettier": "^2.3.0", "sass": "^1.32.12", "tailwind-scrollbar": "^1.3.1", "tailwindcss": "^3.0.6", "vite": "^2.6.1"}, "dependencies": {"@headlessui/vue": "^1.4.0", "@heroicons/vue": "^1.0.1", "@popperjs/core": "^2.9.2", "@stripe/stripe-js": "^1.21.2", "@tailwindcss/line-clamp": "^0.3.0", "@tiptap/core": "^2.0.0-beta.85", "@tiptap/extension-text-align": "^2.0.0-beta.29", "@tiptap/starter-kit": "^2.0.0-beta.81", "@tiptap/vue-3": "^2.0.0-beta.38", "@vuelidate/components": "^1.1.12", "@vuelidate/core": "^2.0.0-alpha.32", "@vuelidate/validators": "^2.0.0-alpha.25", "@vueuse/core": "^6.0.0", "axios": "^0.19", "chart.js": "^2.7.3", "express": "^5.1.0", "guid": "0.0.12", "lodash": "^4.17.13", "maska": "^1.4.6", "mini-svg-data-uri": "^1.3.3", "moment": "^2.29.1", "pinia": "^2.0.4", "v-money3": "^3.13.5", "v-tooltip": "^4.0.0-alpha.1", "vue": "^3.2.0-beta.5", "vue-flatpickr-component": "^9.0.3", "vue-i18n": "^9.1.7", "vue-router": "^4.0.8", "vue3-colorpicker": "^1.0.5", "vuedraggable": "^4.1.0"}}