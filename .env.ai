# AI Provider Configuration
# Kopieer dit bestand naar .env.ai.local en vul je eigen API keys in

# OpenRouter API Key
# Verkrijg je API key van: https://openrouter.ai/keys
OPENROUTER_API_KEY=your_openrouter_api_key_here

# Ollama Configuration
# Zorg ervoor dat Ollama draait op localhost:11434
OLLAMA_BASE_URL=http://localhost:11434

# LM Studio Configuration  
# Zorg ervoor dat LM Studio server draait op localhost:1234
LMSTUDIO_BASE_URL=http://localhost:1234/v1

# GitHub Copilot Alternative Settings
COPILOT_PROXY_URL=http://localhost:11434
COPILOT_MODEL=codellama:7b

# Continue.dev Settings
CONTINUE_TELEMETRY=false
CONTINUE_MODEL_PROVIDER=ollama
