#!/bin/bash

# Innovars AI System CRM - Local Development Server
echo "🚀 Starting Innovars AI System CRM (Local Mode)..."

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Check if we have PHP
if command -v php &> /dev/null; then
    PHP_CMD="php"
elif command -v /opt/homebrew/bin/php &> /dev/null; then
    PHP_CMD="/opt/homebrew/bin/php"
elif command -v /usr/local/bin/php &> /dev/null; then
    PHP_CMD="/usr/local/bin/php"
else
    print_warning "PHP not found. Please install PHP or use Docker."
    exit 1
fi

print_status "Using PHP: $PHP_CMD"

# Check PHP version
PHP_VERSION=$($PHP_CMD --version | head -n 1)
print_status "PHP Version: $PHP_VERSION"

# Create SQLite database for local development
print_status "Setting up local SQLite database..."
mkdir -p database
touch database/database.sqlite

# Create local .env file
if [ ! -f .env ]; then
    print_status "Creating local environment file..."
    cat > .env << EOF
APP_NAME="Innovars AI System CRM"
APP_ENV=local
APP_KEY=base64:kgk/4DW1vEVy7aEvet5FPp5un6PIGe/so8H0mvoUtW0=
APP_DEBUG=true
APP_URL=http://localhost:8000

LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=sqlite
DB_DATABASE=database/database.sqlite

BROADCAST_DRIVER=log
CACHE_DRIVER=file
FILESYSTEM_DISK=local
QUEUE_CONNECTION=sync
SESSION_DRIVER=file
SESSION_LIFETIME=120

# Innovars AI Branding
COMPANY_NAME="Innovars AI System"
COMPANY_LOGO="/img/innovars-logo.svg"
COMPANY_LOGO_WHITE="/img/innovars-logo-white.svg"

# AI Features
AI_ENABLED=true
AI_PROVIDER=local
EOF
fi

# Install Composer dependencies
if command -v composer &> /dev/null; then
    print_status "Installing PHP dependencies..."
    composer install --no-dev --optimize-autoloader
else
    print_warning "Composer not found. Please install Composer."
fi

# Generate app key
print_status "Generating application key..."
$PHP_CMD artisan key:generate

# Run migrations
print_status "Running database migrations..."
$PHP_CMD artisan migrate --force

# Clear caches
print_status "Clearing caches..."
$PHP_CMD artisan config:clear
$PHP_CMD artisan cache:clear

# Install npm dependencies and build assets
if command -v npm &> /dev/null; then
    print_status "Installing frontend dependencies..."
    npm install
    print_status "Building frontend assets..."
    npm run build
else
    print_warning "npm not found. Frontend assets may not work properly."
fi

# Start the development server
print_success "🎉 Starting Innovars AI System CRM!"
echo ""
echo "📱 Access your CRM at: http://localhost:8000"
echo "🗄️  Database: SQLite (database/database.sqlite)"
echo "📊 Admin Panel: http://localhost:8000/admin"
echo ""
echo "Press Ctrl+C to stop the server"
echo ""

$PHP_CMD artisan serve --host=0.0.0.0 --port=8000