const colors = require('tailwindcss/colors')
const svgToDataUri = require('mini-svg-data-uri')

function withOpacityValue(cssVariable) {
  return ({ opacityVariable, opacityValue }) => {
    if (opacityValue !== undefined) {
      return `rgba(var(${cssVariable}), ${opacityValue})`;
    }
    if (opacityVariable !== undefined) {
      return `rgba(var(${cssVariable}), var(${opacityVariable}, 1))`;
    }
    return `rgb(var(${cssVariable}))`;
  };
}

module.exports = {
  content: [
    './resources/views/**/*.php',
    './resources/scripts/**/*.js',
    './resources/scripts/**/*.vue',
  ],
  theme: {
    extend: {
      colors: {
        primary: {
          50: withOpacityValue('--color-primary-50'),
          100: withOpacityValue('--color-primary-100'),
          200: withOpacityValue('--color-primary-200'),
          300: withOpacityValue('--color-primary-300'),
          400: withOpacityValue('--color-primary-400'),
          500: withOpacityValue('--color-primary-500'),
          600: withOpacityValue('--color-primary-600'),
          700: withOpacityValue('--color-primary-700'),
          800: withOpacityValue('--color-primary-800'),
          900: withOpacityValue('--color-primary-900'),
        },
        secondary: {
          50: withOpacityValue('--color-secondary-50'),
          100: withOpacityValue('--color-secondary-100'),
          200: withOpacityValue('--color-secondary-200'),
          300: withOpacityValue('--color-secondary-300'),
          400: withOpacityValue('--color-secondary-400'),
          500: withOpacityValue('--color-secondary-500'),
          600: withOpacityValue('--color-secondary-600'),
          700: withOpacityValue('--color-secondary-700'),
          800: withOpacityValue('--color-secondary-800'),
          900: withOpacityValue('--color-secondary-900'),
        },
        success: {
          50: withOpacityValue('--color-success-50'),
          100: withOpacityValue('--color-success-100'),
          200: withOpacityValue('--color-success-200'),
          300: withOpacityValue('--color-success-300'),
          400: withOpacityValue('--color-success-400'),
          500: withOpacityValue('--color-success-500'),
          600: withOpacityValue('--color-success-600'),
          700: withOpacityValue('--color-success-700'),
          800: withOpacityValue('--color-success-800'),
          900: withOpacityValue('--color-success-900'),
        },
        warning: {
          50: withOpacityValue('--color-warning-50'),
          100: withOpacityValue('--color-warning-100'),
          200: withOpacityValue('--color-warning-200'),
          300: withOpacityValue('--color-warning-300'),
          400: withOpacityValue('--color-warning-400'),
          500: withOpacityValue('--color-warning-500'),
          600: withOpacityValue('--color-warning-600'),
          700: withOpacityValue('--color-warning-700'),
          800: withOpacityValue('--color-warning-800'),
          900: withOpacityValue('--color-warning-900'),
        },
        error: {
          50: withOpacityValue('--color-error-50'),
          100: withOpacityValue('--color-error-100'),
          200: withOpacityValue('--color-error-200'),
          300: withOpacityValue('--color-error-300'),
          400: withOpacityValue('--color-error-400'),
          500: withOpacityValue('--color-error-500'),
          600: withOpacityValue('--color-error-600'),
          700: withOpacityValue('--color-error-700'),
          800: withOpacityValue('--color-error-800'),
          900: withOpacityValue('--color-error-900'),
        },
        black: '#040405',
        red: colors.red,
        teal: colors.teal,
        gray: colors.slate,
      },
      spacing: {
        88: '22rem',
        '18': '4.5rem',
        '72': '18rem',
        '84': '21rem',
        '96': '24rem',
      },
      borderRadius: {
        'xl': '0.75rem',
        '2xl': '1rem',
        '3xl': '1.5rem',
      },
      animation: {
        'fade-in': 'fadeIn 0.5s ease-in-out',
        'slide-up': 'slideUp 0.3s ease-out',
        'bounce-in': 'bounceIn 0.6s ease-out',
        'pulse-slow': 'pulse 3s infinite',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        slideUp: {
          '0%': { transform: 'translateY(10px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        bounceIn: {
          '0%': { transform: 'scale(0.3)', opacity: '0' },
          '50%': { transform: 'scale(1.05)' },
          '70%': { transform: 'scale(0.9)' },
          '100%': { transform: 'scale(1)', opacity: '1' },
        },
      },
      backgroundImage: (theme) => ({
        'multiselect-caret': `url("${svgToDataUri(
          `<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
  <path fill-rule="evenodd" d="M10 3a1 1 0 01.707.293l3 3a1 1 0 01-1.414 1.414L10 5.414 7.707 7.707a1 1 0 01-1.414-1.414l3-3A1 1 0 0110 3zm-3.707 9.293a1 1 0 011.414 0L10 14.586l2.293-2.293a1 1 0 011.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd" />
</svg>`
        )}")`,
        'multiselect-spinner': `url("${svgToDataUri(
          `<svg viewBox="0 0 512 512" fill="${theme(
            'colors.primary.500'
          )}" xmlns="http://www.w3.org/2000/svg"><path d="M456.433 371.72l-27.79-16.045c-7.192-4.152-10.052-13.136-6.487-20.636 25.82-54.328 23.566-118.602-6.768-171.03-30.265-52.529-84.802-86.621-144.76-91.424C262.35 71.922 256 64.953 256 56.649V24.56c0-9.31 7.916-16.609 17.204-15.96 81.795 5.717 156.412 51.902 197.611 123.408 41.301 71.385 43.99 159.096 8.042 232.792-4.082 8.369-14.361 11.575-22.424 6.92z"></path></svg>`
        )}")`,
        'multiselect-remove': `url("${svgToDataUri(
          `<svg viewBox="0 0 320 512" fill="${theme(
            'colors.white'
          )}" xmlns="http://www.w3.org/2000/svg"><path d="M207.6 256l107.72-107.72c6.23-6.23 6.23-16.34 0-22.58l-25.03-25.03c-6.23-6.23-16.34-6.23-22.58 0L160 208.4 52.28 100.68c-6.23-6.23-16.34-6.23-22.58 0L4.68 125.7c-6.23 6.23-6.23 16.34 0 22.58L112.4 256 4.68 363.72c-6.23 6.23-6.23 16.34 0 22.58l25.03 25.03c6.23 6.23 16.34 6.23 22.58 0L160 303.6l107.72 107.72c6.23 6.23 16.34 6.23 22.58 0l25.03-25.03c6.23-6.23 6.23-16.34 0-22.58L207.6 256z"></path></svg>`
        )}")`,
      }),
    },

    fontFamily: {
      base: ['Poppins', 'sans-serif'],
    },
  },
  plugins: [
    require('@tailwindcss/forms'),
    require('@tailwindcss/typography'),
    require('@tailwindcss/aspect-ratio'),
    require('tailwind-scrollbar'),
    require('@rvxlab/tailwind-plugin-ios-full-height'),
    require('@tailwindcss/line-clamp'),
  ],
}
