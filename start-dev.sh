#!/bin/bash

# Crater CRM Development Startup Script
# Dit script start alle benodigde services voor development

set -e

echo "🚀 Starting Crater CRM Development Environment"
echo "=============================================="

# Kleuren
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m'

# Check of we in de juiste directory zijn
if [ ! -f "artisan" ]; then
    echo -e "${RED}❌ Fout: artisan bestand niet gevonden. Zorg dat je in de Crater root directory bent.${NC}"
    exit 1
fi

# Check of .env bestaat
if [ ! -f ".env" ]; then
    echo -e "${YELLOW}⚠️  .env bestand niet gevonden. Kopiëren van .env.example...${NC}"
    cp .env.example .env
    echo -e "${GREEN}✅ .env bestand aangemaakt${NC}"
fi

# Check dependencies
echo -e "${YELLOW}📦 Checking dependencies...${NC}"

if [ ! -d "vendor" ]; then
    echo -e "${YELLOW}📦 Installing PHP dependencies...${NC}"
    composer install
fi

if [ ! -d "node_modules" ]; then
    echo -e "${YELLOW}📦 Installing Node.js dependencies...${NC}"
    npm install
fi

# Database setup
echo -e "${YELLOW}🗄️  Setting up database...${NC}"
php artisan key:generate --force
php artisan migrate --force
php artisan storage:link

# Clear caches
echo -e "${YELLOW}🧹 Clearing caches...${NC}"
php artisan config:clear
php artisan cache:clear
php artisan view:clear
php artisan route:clear

# Start services in background
echo -e "${YELLOW}🌐 Starting Laravel development server...${NC}"
php artisan serve --host=0.0.0.0 --port=8000 &
LARAVEL_PID=$!

echo -e "${YELLOW}⚡ Starting Vite development server...${NC}"
npm run dev &
VITE_PID=$!

# Wait a moment for servers to start
sleep 3

echo ""
echo -e "${GREEN}🎉 Development environment started successfully!${NC}"
echo ""
echo -e "${BLUE}📋 Available URLs:${NC}"
echo -e "   Laravel: ${YELLOW}http://localhost:8000${NC}"
echo -e "   Vite HMR: ${YELLOW}http://localhost:5173${NC}"
echo ""
echo -e "${BLUE}💡 Tips:${NC}"
echo -e "   • Open ${YELLOW}http://localhost:8000${NC} in je browser"
echo -e "   • Changes worden automatisch herladen"
echo -e "   • Druk Ctrl+C om beide servers te stoppen"
echo ""

# Function to cleanup on exit
cleanup() {
    echo ""
    echo -e "${YELLOW}🛑 Stopping development servers...${NC}"
    kill $LARAVEL_PID 2>/dev/null || true
    kill $VITE_PID 2>/dev/null || true
    echo -e "${GREEN}✅ Development servers stopped${NC}"
    exit 0
}

# Trap Ctrl+C
trap cleanup INT

# Wait for user to stop
echo -e "${BLUE}Press Ctrl+C to stop all servers...${NC}"
wait
