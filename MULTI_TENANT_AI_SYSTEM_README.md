# Multi-Tenant AI Agents CRM System

## Overzicht

Dit systeem is getransformeerd van een traditionele Crater CRM naar een geavanceerde multi-tenant AI agents platform voor bedrijven. Het systeem ondersteunt verschillende bedrijfstypes met specifieke AI-agenten en WhatsApp integratie.

## Ondersteunde Bedrijfstypes

### 1. Taxi/Transport Services
- **AI Agents**: Booking Agent, Route Optimizer, Customer Service
- **Features**: Real-time booking, GPS tracking, driver management, fare calculation
- **WhatsApp**: Geïntegreerd voor booking confirmaties en updates

### 2. Construction/Building
- **AI Agents**: Project Manager, Material Tracker, Customer Service
- **Features**: Project management, material tracking, progress monitoring
- **WhatsApp**: Client communication, progress updates

### 3. Car Rental
- **AI Agents**: Fleet Manager, Booking Agent, Customer Service
- **Features**: Fleet management, booking system, maintenance tracking
- **WhatsApp**: Booking confirmaties, vehicle status updates

### 4. Restaurant/Food Service
- **AI Agents**: Order Manager, Inventory Tracker, Customer Service
- **Features**: Order management, inventory tracking, delivery coordination
- **WhatsApp**: Order confirmaties, delivery updates

### 5. E-commerce
- **AI Agents**: Sales Agent, Inventory Manager, Customer Service
- **Features**: Product catalog, order processing, shipping coordination
- **WhatsApp**: Order updates, customer support

### 6. Healthcare/Medical
- **AI Agents**: Appointment Scheduler, Patient Manager, Customer Service
- **Features**: Appointment scheduling, patient management, medical records
- **WhatsApp**: Appointment reminders, health updates

### 7. Real Estate
- **AI Agents**: Property Matcher, Client Manager, Customer Service
- **Features**: Property listings, client matching, viewing scheduling
- **WhatsApp**: Property updates, viewing confirmaties

## Database Schema

### Core Tables
- `tenants` - Multi-tenant isolation
- `business_types` - Available business types
- `agent_templates` - Pre-configured AI agent templates
- `ai_agents` - Active AI agents per tenant
- `whatsapp_integrations` - WhatsApp Business API integration
- `agent_conversations` - AI agent conversations
- `conversation_messages` - Message history

### Business-Specific Tables
- `taxi_bookings`, `taxi_drivers` - Taxi business
- `construction_projects`, `construction_materials` - Construction business
- `rental_vehicles`, `rental_bookings` - Car rental business
- `automation_rules` - Workflow automation

## Installation & Setup

### 1. Database Migration
```bash
php artisan migrate
php artisan db:seed --class=BusinessTypeSeeder
```

### 2. Environment Configuration
Add to your `.env` file:
```env
# Multi-tenant configuration
TENANT_DOMAIN_SUFFIX=.yourdomain.com
WHATSAPP_API_URL=https://graph.facebook.com/v18.0
WHATSAPP_VERIFY_TOKEN=your_webhook_verify_token

# AI Provider Configuration
OPENAI_API_KEY=your_openai_key
ANTHROPIC_API_KEY=your_anthropic_key
```

### 3. WhatsApp Business API Setup
1. Create Facebook Business Account
2. Set up WhatsApp Business API
3. Configure webhook URL: `https://yourdomain.com/api/webhooks/whatsapp`
4. Add verify token to environment

## API Endpoints

### Tenant Management
```
GET    /api/v1/tenants                 - List all tenants
POST   /api/v1/tenants                 - Create new tenant
GET    /api/v1/tenants/{id}            - Get tenant details
PUT    /api/v1/tenants/{id}            - Update tenant
DELETE /api/v1/tenants/{id}            - Delete tenant
GET    /api/v1/tenants/business-types  - Get available business types
```

### AI Agents
```
GET    /api/v1/agents                  - List tenant's AI agents
POST   /api/v1/agents                  - Create new AI agent
PUT    /api/v1/agents/{id}             - Update AI agent
DELETE /api/v1/agents/{id}             - Delete AI agent
POST   /api/v1/agents/{id}/start       - Start AI agent
POST   /api/v1/agents/{id}/stop        - Stop AI agent
```

### WhatsApp Integration
```
POST   /api/webhooks/whatsapp          - WhatsApp webhook endpoint
GET    /api/webhooks/whatsapp          - Webhook verification
POST   /api/v1/whatsapp/send           - Send WhatsApp message
GET    /api/v1/whatsapp/templates      - Get message templates
```

## Multi-Tenant Architecture

### Tenant Isolation
- **Database Level**: All tables include `tenant_id` for data isolation
- **Middleware**: `TenantMiddleware` ensures proper tenant context
- **Subdomain Support**: `tenant.yourdomain.com` routing
- **Custom Domains**: Support for custom tenant domains

### Business Type Configuration
Each business type has:
- **Features**: Available functionality
- **Agent Templates**: Pre-configured AI agents
- **Dashboard Config**: Custom dashboard layout
- **WhatsApp Settings**: Integration preferences

## AI Agent System

### Agent Types
1. **Customer Service** - General customer support
2. **Booking** - Handle reservations and appointments
3. **Inventory** - Manage stock and materials
4. **Communication** - Automated messaging
5. **Analytics** - Business insights and reporting
6. **Payment** - Payment processing assistance

### Agent Configuration
```php
$agent = AIAgent::create([
    'tenant_id' => $tenant->id,
    'name' => 'Customer Service Bot',
    'agent_type' => 'customer_service',
    'ai_provider' => 'openai',
    'model' => 'gpt-4',
    'capabilities' => ['answer_questions', 'handle_complaints'],
    'triggers' => ['keywords' => ['help', 'support']],
    'whatsapp_enabled' => true,
]);
```

## WhatsApp Integration

### Message Types Supported
- Text messages
- Images and documents
- Audio messages
- Location sharing
- Template messages
- Interactive buttons

### Webhook Handling
The system automatically processes:
- Incoming messages
- Delivery receipts
- Read receipts
- User interactions

## Development Guidelines

### Adding New Business Types
1. Add entry to `BusinessTypeSeeder`
2. Create agent templates in `AgentTemplateSeeder`
3. Add business-specific migrations if needed
4. Create corresponding models and controllers

### Creating Custom AI Agents
1. Extend base `AIAgent` model
2. Implement specific capabilities
3. Configure triggers and responses
4. Test with WhatsApp integration

## Security Considerations

### Data Protection
- Tenant data isolation enforced at database level
- API authentication via Laravel Sanctum
- WhatsApp webhook signature verification
- Encrypted sensitive data storage

### Access Control
- Role-based permissions per tenant
- Super admin access for system management
- Tenant admin access for business management
- User-level permissions for daily operations

## Monitoring & Analytics

### System Metrics
- Tenant activity monitoring
- AI agent performance tracking
- WhatsApp message delivery rates
- Business-specific KPIs

### Logging
- All AI agent interactions logged
- WhatsApp API calls tracked
- System errors and performance metrics
- Audit trail for tenant actions

## Support & Maintenance

### Regular Tasks
- Monitor AI agent performance
- Update WhatsApp templates
- Review tenant usage metrics
- Backup tenant data

### Troubleshooting
- Check AI agent logs for errors
- Verify WhatsApp webhook connectivity
- Monitor database performance
- Review tenant-specific issues

## Future Enhancements

### Planned Features
- Voice message support
- Advanced analytics dashboard
- Multi-language support
- Mobile app integration
- Advanced workflow automation
- Integration with external APIs

### Scalability Considerations
- Database sharding for large tenant volumes
- Redis caching for improved performance
- Load balancing for high availability
- CDN integration for media files

---

Voor technische ondersteuning of vragen over implementatie, raadpleeg de documentatie of neem contact op met het ontwikkelteam.
