# 🚀 Crater CRM - Development Setup Fixed!

## ✅ Wat is er opgelost?

### 1. **Vite Configuratie Ver<PERSON>erd**
- **Hot Module Replacement (HMR)** nu correct geconfigureerd
- **File watching** verbeterd met polling
- **CORS** ingeschakeld voor cross-origin requests
- **Port configuratie** gesynchroniseerd (5173)

### 2. **Laravel Vite Integratie**
- **Dev URL** gecorrigeerd naar juiste port
- **Asset loading** geoptimaliseerd
- **Live reload** nu werkend

### 3. **Development Scripts**
- **Automatische startup** scripts toegevoegd
- **Dependency checking** ingebouwd
- **Service management** geautomatiseerd

## 🎯 Hoe Te Gebruiken

### Optie 1: Volledige Development Environment
```bash
# Start Laravel + Vite samen
./start-dev.sh
```

Dit start:
- <PERSON>vel development server op `http://localhost:8000`
- Vite HMR server op `http://localhost:5173`
- Automatische file watching
- Live reload functionaliteit

### Optie 2: Alleen Vite (<PERSON>s <PERSON> al draait)
```bash
# Start alleen Vite HMR
./start-vite.sh
```

### Optie 3: Handmatig
```bash
# Terminal 1: Laravel
php artisan serve --host=0.0.0.0 --port=8000

# Terminal 2: Vite
npm run dev
```

## 🔧 Wat Er Veranderd Is

### `vite.config.ts`
```typescript
server: {
    host: '0.0.0.0',        // Toegankelijk vanaf andere apparaten
    port: 5173,             // Vaste port
    hmr: { host: 'localhost' }, // HMR configuratie
    watch: { usePolling: true }, // Betere file watching
    cors: true              // CORS ondersteuning
}
```

### `config/vite.php`
```php
'dev_url' => 'http://localhost:5173', // Gecorrigeerde port
```

### `package.json`
```json
"scripts": {
    "dev": "vite --host 0.0.0.0 --port 5173",
    "dev:watch": "vite --host 0.0.0.0 --port 5173 --watch"
}
```

## 🎉 Resultaat

Nu krijg je:

### ✅ **Live Reload**
- Wijzigingen in `.vue` bestanden worden direct zichtbaar
- CSS/SCSS wijzigingen laden automatisch
- JavaScript wijzigingen triggeren page refresh

### ✅ **Hot Module Replacement**
- Vue componenten updaten zonder page refresh
- State blijft behouden tijdens development
- Snellere development cycle

### ✅ **File Watching**
- Alle bestanden in `resources/` worden gemonitord
- Automatische hercompilatie bij wijzigingen
- Polling voor betere compatibiliteit

## 🐛 Troubleshooting

### Vite Start Niet
```bash
# Kill bestaande processen
pkill -f "vite"

# Clear node_modules en herinstalleer
rm -rf node_modules package-lock.json
npm install

# Start opnieuw
./start-vite.sh
```

### Laravel Draait Niet
```bash
# Check .env configuratie
cp .env.example .env
php artisan key:generate

# Database setup
php artisan migrate
php artisan storage:link

# Start Laravel
php artisan serve --host=0.0.0.0 --port=8000
```

### Live Reload Werkt Niet
1. **Check browser console** voor errors
2. **Controleer of beide servers draaien**:
   - Laravel: `http://localhost:8000`
   - Vite: `http://localhost:5173`
3. **Hard refresh**: Ctrl+Shift+R
4. **Clear browser cache**

### Port Conflicts
```bash
# Check welke processen ports gebruiken
lsof -i :8000
lsof -i :5173

# Kill processen indien nodig
kill -9 <PID>
```

## 💡 Development Tips

### 1. **Browser DevTools**
- Open DevTools (F12)
- Check Console voor HMR berichten
- Network tab toont asset loading

### 2. **File Structure**
```
resources/
├── scripts/           # Vue.js applicatie
│   ├── main.js       # Entry point
│   ├── App.vue       # Root component
│   └── ...
├── sass/             # SCSS styles
└── views/            # Blade templates
```

### 3. **Snelle Edits**
- **Vue componenten**: Direct zichtbaar via HMR
- **SCSS wijzigingen**: Automatisch geïnjecteerd
- **PHP/Blade**: Vereist page refresh

### 4. **Performance**
- Gebruik `npm run dev:watch` voor continue watching
- Sluit onnodige browser tabs
- Monitor memory usage bij grote projecten

## 🎯 Volgende Stappen

1. **Start development environment**: `./start-dev.sh`
2. **Open browser**: `http://localhost:8000`
3. **Maak een wijziging** in een Vue component
4. **Zie de live update** in je browser!

---

**🎉 Je development environment is nu volledig geconfigureerd voor live reload!**
