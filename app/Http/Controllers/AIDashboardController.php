<?php

namespace Crater\Http\Controllers;

use Crater\Models\AIAgent;
use Crater\Models\AITask;
use App\Models\SystemLog;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class AIDashboardController extends Controller
{
    /**
     * Get dashboard overview data
     */
    public function index(): JsonResponse
    {
        $agents = AIAgent::all();
        $tasks = AITask::with('creator')->latest()->take(10)->get();
        $logs = SystemLog::latest()->take(20)->get();

        $stats = [
            'agents' => [
                'total' => $agents->count(),
                'running' => $agents->where('status', 'running')->count(),
                'stopped' => $agents->where('status', 'stopped')->count(),
                'error' => $agents->where('status', 'error')->count(),
            ],
            'tasks' => [
                'total' => AITask::count(),
                'pending' => AITask::where('status', 'pending')->count(),
                'running' => AITask::where('status', 'running')->count(),
                'completed' => AITask::where('status', 'completed')->count(),
                'failed' => AITask::where('status', 'failed')->count(),
            ],
            'system' => [
                'uptime' => $this->getSystemUptime(),
                'memory_usage' => $this->getMemoryUsage(),
                'cpu_usage' => $this->getCpuUsage(),
            ]
        ];

        return response()->json([
            'agents' => $agents,
            'tasks' => $tasks,
            'logs' => $logs,
            'stats' => $stats
        ]);
    }

    /**
     * Get all AI agents
     */
    public function getAgents(): JsonResponse
    {
        $agents = AIAgent::all();
        return response()->json($agents);
    }

    /**
     * Start an AI agent
     */
    public function startAgent(Request $request, $id): JsonResponse
    {
        $agent = AIAgent::findOrFail($id);
        
        // Simulate starting the agent (in real implementation, this would start the actual service)
        $agent->update([
            'status' => 'running',
            'last_activity' => now()
        ]);

        SystemLog::create([
            'level' => 'info',
            'category' => 'ai_agent',
            'event' => 'agent_started',
            'message' => "AI Agent '{$agent->name}' started successfully",
            'context' => ['agent_id' => $agent->id],
            'user_id' => auth()->id()
        ]);

        return response()->json([
            'message' => 'Agent started successfully',
            'agent' => $agent
        ]);
    }

    /**
     * Stop an AI agent
     */
    public function stopAgent(Request $request, $id): JsonResponse
    {
        $agent = AIAgent::findOrFail($id);
        
        $agent->update(['status' => 'stopped']);

        SystemLog::create([
            'level' => 'info',
            'category' => 'ai_agent',
            'event' => 'agent_stopped',
            'message' => "AI Agent '{$agent->name}' stopped",
            'context' => ['agent_id' => $agent->id],
            'user_id' => auth()->id()
        ]);

        return response()->json([
            'message' => 'Agent stopped successfully',
            'agent' => $agent
        ]);
    }

    /**
     * Stop all AI agents
     */
    public function stopAllAgents(): JsonResponse
    {
        $runningAgents = AIAgent::where('status', 'running')->get();
        
        AIAgent::where('status', 'running')->update(['status' => 'stopped']);

        SystemLog::create([
            'level' => 'info',
            'category' => 'ai_agent',
            'event' => 'all_agents_stopped',
            'message' => 'All AI agents stopped',
            'context' => ['count' => $runningAgents->count()],
            'user_id' => auth()->id()
        ]);

        return response()->json([
            'message' => 'All agents stopped successfully',
            'count' => $runningAgents->count()
        ]);
    }

    /**
     * Get all tasks
     */
    public function getTasks(): JsonResponse
    {
        $tasks = AITask::with('creator')->latest()->paginate(20);
        return response()->json($tasks);
    }

    /**
     * Create a new task
     */
    public function createTask(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'type' => 'required|string|in:email,analysis,automation,custom',
            'description' => 'nullable|string',
            'parameters' => 'nullable|array',
            'agent_type' => 'nullable|string',
            'schedule' => 'nullable|string',
            'scheduled_at' => 'nullable|date'
        ]);

        $validated['created_by'] = auth()->id();

        $task = AITask::create($validated);

        SystemLog::create([
            'level' => 'info',
            'category' => 'task_scheduler',
            'event' => 'task_created',
            'message' => "New task '{$task->name}' created",
            'context' => ['task_id' => $task->id],
            'user_id' => auth()->id()
        ]);

        return response()->json([
            'message' => 'Task created successfully',
            'task' => $task->load('creator')
        ], 201);
    }

    /**
     * Get system logs
     */
    public function getLogs(Request $request): JsonResponse
    {
        $query = SystemLog::query();

        if ($request->has('level')) {
            $query->where('level', $request->level);
        }

        if ($request->has('category')) {
            $query->where('category', $request->category);
        }

        $logs = $query->latest()->paginate(50);
        return response()->json($logs);
    }

    /**
     * Get system uptime (mock implementation)
     */
    private function getSystemUptime(): string
    {
        return '2 days, 14 hours, 32 minutes';
    }

    /**
     * Get memory usage (mock implementation)
     */
    private function getMemoryUsage(): array
    {
        return [
            'used' => '2.4 GB',
            'total' => '8.0 GB',
            'percentage' => 30
        ];
    }

    /**
     * Get CPU usage (mock implementation)
     */
    private function getCpuUsage(): array
    {
        return [
            'percentage' => 15,
            'cores' => 8
        ];
    }
}
