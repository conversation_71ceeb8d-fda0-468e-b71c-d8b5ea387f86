<?php

namespace Crater\Http\Controllers\V1\Admin;

use Crater\Http\Controllers\Controller;
use Crater\Models\Tenant;
use Crater\Models\BusinessType;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Str;

class TenantController extends Controller
{
    /**
     * Display a listing of tenants.
     */
    public function index(Request $request): JsonResponse
    {
        $tenants = Tenant::with('businessType')
            ->when($request->search, function ($query, $search) {
                $query->where('name', 'like', "%{$search}%")
                      ->orWhere('email', 'like', "%{$search}%");
            })
            ->when($request->business_type_id, function ($query, $businessTypeId) {
                $query->where('business_type_id', $businessTypeId);
            })
            ->when($request->status, function ($query, $status) {
                $query->where('status', $status);
            })
            ->orderBy('created_at', 'desc')
            ->paginate($request->per_page ?? 15);

        return response()->json($tenants);
    }

    /**
     * Store a newly created tenant.
     */
    public function store(Request $request): JsonResponse
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:tenants,email',
            'business_type_id' => 'required|exists:business_types,id',
            'phone' => 'nullable|string|max:20',
            'address' => 'nullable|string',
            'subscription_plan' => 'nullable|string',
        ]);

        $businessType = BusinessType::findOrFail($request->business_type_id);

        $tenant = Tenant::create([
            'name' => $request->name,
            'slug' => Str::slug($request->name),
            'domain' => null, // Will be set later
            'business_type_id' => $request->business_type_id,
            'email' => $request->email,
            'phone' => $request->phone,
            'address' => $request->address,
            'status' => 'trial',
            'trial_ends_at' => now()->addDays(14),
            'subscription_plan' => $request->subscription_plan ?? 'basic',
            'settings' => [
                'features' => $businessType->features,
                'whatsapp_enabled' => $businessType->whatsapp_enabled,
            ],
        ]);

        // Create default AI agents based on business type templates
        $this->createDefaultAgents($tenant);

        return response()->json([
            'message' => 'Tenant created successfully',
            'tenant' => $tenant->load('businessType'),
        ], 201);
    }

    /**
     * Display the specified tenant.
     */
    public function show(Tenant $tenant): JsonResponse
    {
        $tenant->load([
            'businessType',
            'users',
            'aiAgents',
            'whatsappIntegrations',
        ]);

        return response()->json($tenant);
    }

    /**
     * Update the specified tenant.
     */
    public function update(Request $request, Tenant $tenant): JsonResponse
    {
        $request->validate([
            'name' => 'sometimes|string|max:255',
            'email' => 'sometimes|email|unique:tenants,email,' . $tenant->id,
            'phone' => 'nullable|string|max:20',
            'address' => 'nullable|string',
            'status' => 'sometimes|in:trial,active,suspended,cancelled',
            'subscription_plan' => 'nullable|string',
        ]);

        $tenant->update($request->only([
            'name', 'email', 'phone', 'address', 'status', 'subscription_plan'
        ]));

        return response()->json([
            'message' => 'Tenant updated successfully',
            'tenant' => $tenant->load('businessType'),
        ]);
    }

    /**
     * Remove the specified tenant.
     */
    public function destroy(Tenant $tenant): JsonResponse
    {
        $tenant->delete();

        return response()->json([
            'message' => 'Tenant deleted successfully',
        ]);
    }

    /**
     * Get business types for tenant creation.
     */
    public function businessTypes(): JsonResponse
    {
        $businessTypes = BusinessType::active()->ordered()->get();

        return response()->json($businessTypes);
    }

    /**
     * Update tenant settings.
     */
    public function updateSettings(Request $request, Tenant $tenant): JsonResponse
    {
        $request->validate([
            'settings' => 'required|array',
        ]);

        $tenant->update(['settings' => $request->settings]);

        return response()->json([
            'message' => 'Settings updated successfully',
            'tenant' => $tenant,
        ]);
    }

    /**
     * Activate tenant.
     */
    public function activate(Tenant $tenant): JsonResponse
    {
        $tenant->update(['status' => 'active']);

        return response()->json([
            'message' => 'Tenant activated successfully',
            'tenant' => $tenant,
        ]);
    }

    /**
     * Suspend tenant.
     */
    public function suspend(Tenant $tenant): JsonResponse
    {
        $tenant->update(['status' => 'suspended']);

        return response()->json([
            'message' => 'Tenant suspended successfully',
            'tenant' => $tenant,
        ]);
    }

    /**
     * Get tenant statistics.
     */
    public function statistics(): JsonResponse
    {
        $stats = [
            'total_tenants' => Tenant::count(),
            'active_tenants' => Tenant::where('status', 'active')->count(),
            'trial_tenants' => Tenant::where('status', 'trial')->count(),
            'suspended_tenants' => Tenant::where('status', 'suspended')->count(),
            'business_type_distribution' => Tenant::join('business_types', 'tenants.business_type_id', '=', 'business_types.id')
                ->selectRaw('business_types.name, COUNT(*) as count')
                ->groupBy('business_types.name')
                ->get(),
            'recent_signups' => Tenant::where('created_at', '>=', now()->subDays(30))->count(),
        ];

        return response()->json($stats);
    }

    /**
     * Create default AI agents for a tenant based on business type.
     */
    private function createDefaultAgents(Tenant $tenant): void
    {
        $agentTemplates = $tenant->businessType->agentTemplates()
            ->where('is_required', true)
            ->get();

        foreach ($agentTemplates as $template) {
            $tenant->aiAgents()->create([
                'agent_template_id' => $template->id,
                'name' => $template->name,
                'agent_type' => $template->agent_type,
                'ai_provider' => 'openai', // Default provider
                'model' => 'gpt-3.5-turbo', // Default model
                'status' => 'stopped',
                'configuration' => $template->default_config,
                'capabilities' => $template->capabilities,
                'triggers' => $template->triggers,
                'responses' => $template->responses,
                'description' => $template->description,
                'is_active' => true,
                'whatsapp_enabled' => $tenant->businessType->whatsapp_enabled,
            ]);
        }
    }
}
