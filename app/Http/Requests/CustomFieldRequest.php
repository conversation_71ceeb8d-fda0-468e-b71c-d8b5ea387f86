<?php

namespace Crater\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class CustomFieldRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'name' => 'required',
            'label' => 'required',
            'model_type' => 'required',
            'order' => 'required',
            'type' => 'required',
            'is_required' => 'required|boolean',
            'options' => 'array',
            'placeholder' => 'string|nullable',
        ];
    }
}
