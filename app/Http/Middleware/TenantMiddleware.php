<?php

namespace Crater\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Crater\Models\Tenant;
use Illuminate\Support\Facades\Auth;

class TenantMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        $tenant = $this->resolveTenant($request);

        if (!$tenant) {
            return response()->json([
                'error' => 'Tenant not found or inactive'
            ], 404);
        }

        // Check if tenant is active
        if (!$tenant->isActive()) {
            return response()->json([
                'error' => 'Tenant account is suspended or inactive'
            ], 403);
        }

        // Set tenant in request for use in controllers
        $request->merge(['tenant' => $tenant]);

        // Set tenant context for the application
        app()->instance('tenant', $tenant);

        return $next($request);
    }

    /**
     * Resolve tenant from request.
     */
    private function resolveTenant(Request $request): ?Tenant
    {
        // Try to resolve tenant from subdomain
        $host = $request->getHost();
        $subdomain = $this->extractSubdomain($host);

        if ($subdomain) {
            $tenant = Tenant::where('slug', $subdomain)->first();
            if ($tenant) {
                return $tenant;
            }
        }

        // Try to resolve tenant from custom domain
        $tenant = Tenant::where('domain', $host)->first();
        if ($tenant) {
            return $tenant;
        }

        // Try to resolve tenant from authenticated user
        $user = Auth::user();
        if ($user && $user->tenant_id) {
            return Tenant::find($user->tenant_id);
        }

        // Try to resolve tenant from header (for API calls)
        $tenantId = $request->header('X-Tenant-ID');
        if ($tenantId) {
            return Tenant::find($tenantId);
        }

        return null;
    }

    /**
     * Extract subdomain from host.
     */
    private function extractSubdomain(string $host): ?string
    {
        $parts = explode('.', $host);
        
        // If we have at least 3 parts (subdomain.domain.tld)
        if (count($parts) >= 3) {
            return $parts[0];
        }

        return null;
    }
}
