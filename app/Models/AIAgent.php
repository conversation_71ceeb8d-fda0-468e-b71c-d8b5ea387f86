<?php

namespace Crater\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class AIAgent extends Model
{
    use HasFactory;

    protected $table = 'ai_agents';

    protected $fillable = [
        'name',
        'type',
        'model',
        'status',
        'configuration',
        'description',
        'last_activity',
        'total_requests',
        'successful_requests',
        'failed_requests',
        'average_response_time',
        'auto_start'
    ];

    protected $casts = [
        'configuration' => 'array',
        'last_activity' => 'datetime',
        'auto_start' => 'boolean',
        'average_response_time' => 'decimal:2'
    ];

    /**
     * Get tasks assigned to this agent
     */
    public function tasks()
    {
        return $this->hasMany(AITask::class, 'agent_type', 'type');
    }

    /**
     * Check if agent is currently running
     */
    public function isRunning(): bool
    {
        return $this->status === 'running';
    }

    /**
     * Check if agent is healthy (recent activity)
     */
    public function isHealthy(): bool
    {
        if (!$this->last_activity) {
            return false;
        }

        return $this->last_activity->diffInMinutes(now()) < 30;
    }

    /**
     * Get success rate percentage
     */
    public function getSuccessRateAttribute(): float
    {
        if ($this->total_requests === 0) {
            return 0;
        }

        return round(($this->successful_requests / $this->total_requests) * 100, 2);
    }

    /**
     * Update agent statistics
     */
    public function updateStats(bool $success, float $responseTime = null)
    {
        $this->increment('total_requests');
        
        if ($success) {
            $this->increment('successful_requests');
        } else {
            $this->increment('failed_requests');
        }

        if ($responseTime !== null) {
            // Calculate new average response time
            $totalTime = $this->average_response_time * ($this->total_requests - 1);
            $newAverage = ($totalTime + $responseTime) / $this->total_requests;
            $this->update(['average_response_time' => $newAverage]);
        }

        $this->update(['last_activity' => now()]);
    }

    /**
     * Scope for active agents
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'running');
    }

    /**
     * Scope for auto-start agents
     */
    public function scopeAutoStart($query)
    {
        return $query->where('auto_start', true);
    }
}
