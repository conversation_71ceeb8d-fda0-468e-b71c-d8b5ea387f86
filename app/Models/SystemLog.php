<?php

namespace Crater\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SystemLog extends Model
{
    use HasFactory;

    protected $fillable = [
        'level',
        'category',
        'event',
        'message',
        'context',
    ];

    protected $casts = [
        'context' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Scope for filtering by log level
     */
    public function scopeLevel($query, $level)
    {
        return $query->where('level', $level);
    }

    /**
     * Scope for filtering by category
     */
    public function scopeCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    /**
     * Scope for recent logs
     */
    public function scopeRecent($query, $hours = 24)
    {
        return $query->where('created_at', '>=', now()->subHours($hours));
    }

    /**
     * Get formatted log level with color
     */
    public function getFormattedLevelAttribute()
    {
        $colors = [
            'debug' => 'text-gray-500',
            'info' => 'text-blue-500',
            'warning' => 'text-yellow-500',
            'error' => 'text-red-500',
            'critical' => 'text-red-700',
        ];

        return [
            'level' => strtoupper($this->level),
            'color' => $colors[$this->level] ?? 'text-gray-500',
        ];
    }

    /**
     * Create a new log entry
     */
    public static function createLog($level, $category, $event, $message, $context = [])
    {
        return static::create([
            'level' => $level,
            'category' => $category,
            'event' => $event,
            'message' => $message,
            'context' => $context,
        ]);
    }

    /**
     * Log info message
     */
    public static function info($category, $event, $message, $context = [])
    {
        return static::createLog('info', $category, $event, $message, $context);
    }

    /**
     * Log warning message
     */
    public static function warning($category, $event, $message, $context = [])
    {
        return static::createLog('warning', $category, $event, $message, $context);
    }

    /**
     * Log error message
     */
    public static function error($category, $event, $message, $context = [])
    {
        return static::createLog('error', $category, $event, $message, $context);
    }

    /**
     * Log debug message
     */
    public static function debug($category, $event, $message, $context = [])
    {
        return static::createLog('debug', $category, $event, $message, $context);
    }
}
