<?php

namespace Crater\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class AgentConversation extends Model
{
    use HasFactory;

    protected $fillable = [
        'tenant_id',
        'ai_agent_id',
        'customer_id',
        'whatsapp_integration_id',
        'whatsapp_chat_id',
        'customer_phone',
        'customer_name',
        'conversation_type',
        'status',
        'context',
        'last_message_at',
        'resolved_at',
        'escalated_at',
        'escalated_to_user_id',
        'satisfaction_rating',
        'notes',
    ];

    protected $casts = [
        'context' => 'array',
        'last_message_at' => 'datetime',
        'resolved_at' => 'datetime',
        'escalated_at' => 'datetime',
    ];

    /**
     * Get the tenant that owns the conversation.
     */
    public function tenant(): BelongsTo
    {
        return $this->belongsTo(Tenant::class);
    }

    /**
     * Get the AI agent handling this conversation.
     */
    public function aiAgent(): BelongsTo
    {
        return $this->belongsTo(AIAgent::class);
    }

    /**
     * Get the customer in this conversation.
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    /**
     * Get the WhatsApp integration for this conversation.
     */
    public function whatsappIntegration(): BelongsTo
    {
        return $this->belongsTo(WhatsappIntegration::class);
    }

    /**
     * Get the user this conversation was escalated to.
     */
    public function escalatedToUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'escalated_to_user_id');
    }

    /**
     * Get the messages in this conversation.
     */
    public function messages(): HasMany
    {
        return $this->hasMany(ConversationMessage::class)->orderBy('created_at');
    }

    /**
     * Get the latest message in this conversation.
     */
    public function latestMessage()
    {
        return $this->hasOne(ConversationMessage::class)->latestOfMany();
    }

    /**
     * Check if conversation is active.
     */
    public function isActive(): bool
    {
        return $this->status === 'active';
    }

    /**
     * Check if conversation is resolved.
     */
    public function isResolved(): bool
    {
        return $this->status === 'resolved';
    }

    /**
     * Check if conversation is escalated.
     */
    public function isEscalated(): bool
    {
        return $this->status === 'escalated';
    }

    /**
     * Resolve the conversation.
     */
    public function resolve(int $rating = null, string $notes = null): void
    {
        $this->update([
            'status' => 'resolved',
            'resolved_at' => now(),
            'satisfaction_rating' => $rating,
            'notes' => $notes,
        ]);
    }

    /**
     * Escalate the conversation to a user.
     */
    public function escalate(int $userId, string $reason = null): void
    {
        $this->update([
            'status' => 'escalated',
            'escalated_at' => now(),
            'escalated_to_user_id' => $userId,
            'notes' => $reason,
        ]);
    }

    /**
     * Archive the conversation.
     */
    public function archive(): void
    {
        $this->update(['status' => 'archived']);
    }

    /**
     * Get context value by key.
     */
    public function getContext(string $key, $default = null)
    {
        return data_get($this->context, $key, $default);
    }

    /**
     * Set context value.
     */
    public function setContext(string $key, $value): void
    {
        $context = $this->context ?? [];
        data_set($context, $key, $value);
        $this->context = $context;
        $this->save();
    }

    /**
     * Update last message timestamp.
     */
    public function updateLastMessage(): void
    {
        $this->update(['last_message_at' => now()]);
    }

    /**
     * Scope for active conversations.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope for escalated conversations.
     */
    public function scopeEscalated($query)
    {
        return $query->where('status', 'escalated');
    }

    /**
     * Scope for resolved conversations.
     */
    public function scopeResolved($query)
    {
        return $query->where('status', 'resolved');
    }

    /**
     * Scope for conversations by phone number.
     */
    public function scopeByPhone($query, string $phone)
    {
        return $query->where('customer_phone', $phone);
    }
}
