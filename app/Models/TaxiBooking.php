<?php

namespace Crater\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class TaxiBooking extends Model
{
    use HasFactory;

    protected $fillable = [
        'tenant_id',
        'customer_id',
        'driver_id',
        'booking_number',
        'customer_name',
        'customer_phone',
        'pickup_address',
        'pickup_latitude',
        'pickup_longitude',
        'destination_address',
        'destination_latitude',
        'destination_longitude',
        'pickup_time',
        'actual_pickup_time',
        'arrival_time',
        'actual_arrival_time',
        'status',
        'fare_estimate',
        'actual_fare',
        'distance_km',
        'duration_minutes',
        'payment_method',
        'payment_status',
        'special_requests',
        'rating',
        'feedback',
        'cancelled_reason',
        'cancelled_by',
    ];

    protected $casts = [
        'pickup_time' => 'datetime',
        'actual_pickup_time' => 'datetime',
        'arrival_time' => 'datetime',
        'actual_arrival_time' => 'datetime',
        'pickup_latitude' => 'decimal:8',
        'pickup_longitude' => 'decimal:8',
        'destination_latitude' => 'decimal:8',
        'destination_longitude' => 'decimal:8',
        'fare_estimate' => 'decimal:2',
        'actual_fare' => 'decimal:2',
        'distance_km' => 'decimal:2',
    ];

    /**
     * Get the tenant that owns the booking.
     */
    public function tenant(): BelongsTo
    {
        return $this->belongsTo(Tenant::class);
    }

    /**
     * Get the customer for this booking.
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    /**
     * Get the driver assigned to this booking.
     */
    public function driver(): BelongsTo
    {
        return $this->belongsTo(TaxiDriver::class, 'driver_id');
    }

    /**
     * Check if booking is active.
     */
    public function isActive(): bool
    {
        return in_array($this->status, ['pending', 'confirmed', 'driver_assigned', 'en_route', 'arrived']);
    }

    /**
     * Check if booking is completed.
     */
    public function isCompleted(): bool
    {
        return $this->status === 'completed';
    }

    /**
     * Check if booking is cancelled.
     */
    public function isCancelled(): bool
    {
        return $this->status === 'cancelled';
    }

    /**
     * Assign driver to booking.
     */
    public function assignDriver(int $driverId): void
    {
        $this->update([
            'driver_id' => $driverId,
            'status' => 'driver_assigned',
        ]);
    }

    /**
     * Mark driver as en route.
     */
    public function markEnRoute(): void
    {
        $this->update(['status' => 'en_route']);
    }

    /**
     * Mark driver as arrived.
     */
    public function markArrived(): void
    {
        $this->update([
            'status' => 'arrived',
            'actual_pickup_time' => now(),
        ]);
    }

    /**
     * Start the trip.
     */
    public function startTrip(): void
    {
        $this->update(['status' => 'in_progress']);
    }

    /**
     * Complete the booking.
     */
    public function complete(float $actualFare = null, float $distance = null, int $duration = null): void
    {
        $this->update([
            'status' => 'completed',
            'actual_arrival_time' => now(),
            'actual_fare' => $actualFare ?? $this->fare_estimate,
            'distance_km' => $distance,
            'duration_minutes' => $duration,
        ]);
    }

    /**
     * Cancel the booking.
     */
    public function cancel(string $reason, string $cancelledBy): void
    {
        $this->update([
            'status' => 'cancelled',
            'cancelled_reason' => $reason,
            'cancelled_by' => $cancelledBy,
        ]);
    }

    /**
     * Rate the trip.
     */
    public function rate(int $rating, string $feedback = null): void
    {
        $this->update([
            'rating' => $rating,
            'feedback' => $feedback,
        ]);
    }

    /**
     * Calculate estimated fare based on distance.
     */
    public function calculateEstimatedFare(float $baseRate = 5.0, float $perKmRate = 2.0): float
    {
        if (!$this->distance_km) {
            return $baseRate;
        }

        return $baseRate + ($this->distance_km * $perKmRate);
    }

    /**
     * Get pickup coordinates.
     */
    public function getPickupCoordinates(): array
    {
        return [
            'latitude' => $this->pickup_latitude,
            'longitude' => $this->pickup_longitude,
        ];
    }

    /**
     * Get destination coordinates.
     */
    public function getDestinationCoordinates(): array
    {
        return [
            'latitude' => $this->destination_latitude,
            'longitude' => $this->destination_longitude,
        ];
    }

    /**
     * Scope for active bookings.
     */
    public function scopeActive($query)
    {
        return $query->whereIn('status', ['pending', 'confirmed', 'driver_assigned', 'en_route', 'arrived', 'in_progress']);
    }

    /**
     * Scope for completed bookings.
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    /**
     * Scope for cancelled bookings.
     */
    public function scopeCancelled($query)
    {
        return $query->where('status', 'cancelled');
    }

    /**
     * Scope for bookings by status.
     */
    public function scopeByStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope for bookings by customer phone.
     */
    public function scopeByCustomerPhone($query, string $phone)
    {
        return $query->where('customer_phone', $phone);
    }
}
