<?php

namespace Crater\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class AITask extends Model
{
    use HasFactory;

    protected $table = 'ai_tasks';

    protected $fillable = [
        'name',
        'type',
        'status',
        'description',
        'parameters',
        'result',
        'agent_type',
        'schedule',
        'scheduled_at',
        'started_at',
        'completed_at',
        'error_message',
        'retry_count',
        'max_retries',
        'created_by'
    ];

    protected $casts = [
        'parameters' => 'array',
        'result' => 'array',
        'scheduled_at' => 'datetime',
        'started_at' => 'datetime',
        'completed_at' => 'datetime'
    ];

    /**
     * Get the user who created this task
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the AI agent for this task
     */
    public function agent()
    {
        return $this->belongsTo(AIAgent::class, 'agent_type', 'type');
    }

    /**
     * Check if task is pending
     */
    public function isPending(): bool
    {
        return $this->status === 'pending';
    }

    /**
     * Check if task is running
     */
    public function isRunning(): bool
    {
        return $this->status === 'running';
    }

    /**
     * Check if task is completed
     */
    public function isCompleted(): bool
    {
        return $this->status === 'completed';
    }

    /**
     * Check if task has failed
     */
    public function hasFailed(): bool
    {
        return $this->status === 'failed';
    }

    /**
     * Check if task can be retried
     */
    public function canRetry(): bool
    {
        return $this->hasFailed() && $this->retry_count < $this->max_retries;
    }

    /**
     * Get execution duration in seconds
     */
    public function getExecutionDurationAttribute(): ?int
    {
        if (!$this->started_at || !$this->completed_at) {
            return null;
        }

        return $this->completed_at->diffInSeconds($this->started_at);
    }

    /**
     * Mark task as started
     */
    public function markAsStarted()
    {
        $this->update([
            'status' => 'running',
            'started_at' => now()
        ]);
    }

    /**
     * Mark task as completed
     */
    public function markAsCompleted(array $result = null)
    {
        $this->update([
            'status' => 'completed',
            'completed_at' => now(),
            'result' => $result
        ]);
    }

    /**
     * Mark task as failed
     */
    public function markAsFailed(string $errorMessage)
    {
        $this->update([
            'status' => 'failed',
            'completed_at' => now(),
            'error_message' => $errorMessage,
            'retry_count' => $this->retry_count + 1
        ]);
    }

    /**
     * Scope for pending tasks
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope for running tasks
     */
    public function scopeRunning($query)
    {
        return $query->where('status', 'running');
    }

    /**
     * Scope for scheduled tasks
     */
    public function scopeScheduled($query)
    {
        return $query->whereNotNull('scheduled_at')
                    ->where('scheduled_at', '<=', now())
                    ->where('status', 'pending');
    }
}
