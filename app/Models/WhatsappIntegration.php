<?php

namespace Crater\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class WhatsappIntegration extends Model
{
    use HasFactory;

    protected $fillable = [
        'tenant_id',
        'phone_number_id',
        'business_account_id',
        'access_token',
        'webhook_verify_token',
        'webhook_url',
        'phone_number',
        'display_name',
        'status',
        'message_templates',
        'settings',
        'last_webhook_received',
    ];

    protected $casts = [
        'message_templates' => 'array',
        'settings' => 'array',
        'last_webhook_received' => 'datetime',
    ];

    protected $hidden = [
        'access_token',
        'webhook_verify_token',
    ];

    /**
     * Get the tenant that owns the WhatsApp integration.
     */
    public function tenant(): BelongsTo
    {
        return $this->belongsTo(Tenant::class);
    }

    /**
     * Get the agent conversations for this integration.
     */
    public function agentConversations(): HasMany
    {
        return $this->hasMany(AgentConversation::class);
    }

    /**
     * Check if integration is active.
     */
    public function isActive(): bool
    {
        return $this->status === 'active';
    }

    /**
     * Check if integration is verified.
     */
    public function isVerified(): bool
    {
        return in_array($this->status, ['verified', 'active']);
    }

    /**
     * Get message template by name.
     */
    public function getTemplate(string $name, $default = null)
    {
        return data_get($this->message_templates, $name, $default);
    }

    /**
     * Set message template.
     */
    public function setTemplate(string $name, array $template): void
    {
        $templates = $this->message_templates ?? [];
        $templates[$name] = $template;
        $this->message_templates = $templates;
        $this->save();
    }

    /**
     * Get setting by key.
     */
    public function getSetting(string $key, $default = null)
    {
        return data_get($this->settings, $key, $default);
    }

    /**
     * Set setting.
     */
    public function setSetting(string $key, $value): void
    {
        $settings = $this->settings ?? [];
        data_set($settings, $key, $value);
        $this->settings = $settings;
        $this->save();
    }

    /**
     * Update webhook received timestamp.
     */
    public function updateWebhookReceived(): void
    {
        $this->update(['last_webhook_received' => now()]);
    }

    /**
     * Check if webhook is healthy (recent activity).
     */
    public function isWebhookHealthy(): bool
    {
        if (!$this->last_webhook_received) {
            return false;
        }

        return $this->last_webhook_received->diffInHours(now()) < 24;
    }

    /**
     * Scope for active integrations.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope for verified integrations.
     */
    public function scopeVerified($query)
    {
        return $query->whereIn('status', ['verified', 'active']);
    }
}
