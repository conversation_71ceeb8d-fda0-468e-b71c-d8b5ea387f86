<?php

namespace Crater\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class BusinessType extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'slug',
        'description',
        'icon',
        'features',
        'agent_templates',
        'dashboard_config',
        'whatsapp_enabled',
        'is_active',
        'sort_order',
    ];

    protected $casts = [
        'features' => 'array',
        'agent_templates' => 'array',
        'dashboard_config' => 'array',
        'whatsapp_enabled' => 'boolean',
        'is_active' => 'boolean',
    ];

    /**
     * Get the tenants for the business type.
     */
    public function tenants(): HasMany
    {
        return $this->hasMany(Tenant::class);
    }

    /**
     * Get the agent templates for the business type.
     */
    public function agentTemplates(): HasMany
    {
        return $this->hasMany(AgentTemplate::class);
    }

    /**
     * Scope a query to only include active business types.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope a query to order by sort order.
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('name');
    }

    /**
     * Get feature by key.
     */
    public function getFeature(string $key, $default = null)
    {
        return data_get($this->features, $key, $default);
    }

    /**
     * Check if feature is enabled.
     */
    public function hasFeature(string $feature): bool
    {
        return in_array($feature, $this->features ?? []);
    }

    /**
     * Get dashboard configuration.
     */
    public function getDashboardConfig(string $key = null, $default = null)
    {
        if ($key) {
            return data_get($this->dashboard_config, $key, $default);
        }
        
        return $this->dashboard_config ?? [];
    }
}
