<?php

namespace Crater\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ConversationMessage extends Model
{
    use HasFactory;

    protected $fillable = [
        'agent_conversation_id',
        'whatsapp_message_id',
        'direction',
        'message_type',
        'content',
        'media_url',
        'media_type',
        'location_data',
        'template_name',
        'template_data',
        'delivery_status',
        'read_at',
        'delivered_at',
        'failed_at',
        'error_message',
        'metadata',
    ];

    protected $casts = [
        'location_data' => 'array',
        'template_data' => 'array',
        'metadata' => 'array',
        'read_at' => 'datetime',
        'delivered_at' => 'datetime',
        'failed_at' => 'datetime',
    ];

    /**
     * Get the conversation this message belongs to.
     */
    public function conversation(): BelongsTo
    {
        return $this->belongsTo(AgentConversation::class, 'agent_conversation_id');
    }

    /**
     * Check if message is inbound (from customer).
     */
    public function isInbound(): bool
    {
        return $this->direction === 'inbound';
    }

    /**
     * Check if message is outbound (to customer).
     */
    public function isOutbound(): bool
    {
        return $this->direction === 'outbound';
    }

    /**
     * Check if message is delivered.
     */
    public function isDelivered(): bool
    {
        return $this->delivery_status === 'delivered';
    }

    /**
     * Check if message is read.
     */
    public function isRead(): bool
    {
        return $this->delivery_status === 'read';
    }

    /**
     * Check if message failed.
     */
    public function isFailed(): bool
    {
        return $this->delivery_status === 'failed';
    }

    /**
     * Mark message as delivered.
     */
    public function markAsDelivered(): void
    {
        $this->update([
            'delivery_status' => 'delivered',
            'delivered_at' => now(),
        ]);
    }

    /**
     * Mark message as read.
     */
    public function markAsRead(): void
    {
        $this->update([
            'delivery_status' => 'read',
            'read_at' => now(),
        ]);
    }

    /**
     * Mark message as failed.
     */
    public function markAsFailed(string $error = null): void
    {
        $this->update([
            'delivery_status' => 'failed',
            'failed_at' => now(),
            'error_message' => $error,
        ]);
    }

    /**
     * Get metadata value by key.
     */
    public function getMetadata(string $key, $default = null)
    {
        return data_get($this->metadata, $key, $default);
    }

    /**
     * Set metadata value.
     */
    public function setMetadata(string $key, $value): void
    {
        $metadata = $this->metadata ?? [];
        data_set($metadata, $key, $value);
        $this->metadata = $metadata;
        $this->save();
    }

    /**
     * Get template data value by key.
     */
    public function getTemplateData(string $key, $default = null)
    {
        return data_get($this->template_data, $key, $default);
    }

    /**
     * Get location coordinates.
     */
    public function getLocation(): ?array
    {
        if (!$this->location_data) {
            return null;
        }

        return [
            'latitude' => data_get($this->location_data, 'latitude'),
            'longitude' => data_get($this->location_data, 'longitude'),
            'name' => data_get($this->location_data, 'name'),
            'address' => data_get($this->location_data, 'address'),
        ];
    }

    /**
     * Check if message has media.
     */
    public function hasMedia(): bool
    {
        return !empty($this->media_url);
    }

    /**
     * Check if message is a template message.
     */
    public function isTemplate(): bool
    {
        return $this->message_type === 'template';
    }

    /**
     * Check if message has location.
     */
    public function hasLocation(): bool
    {
        return $this->message_type === 'location' && !empty($this->location_data);
    }

    /**
     * Scope for inbound messages.
     */
    public function scopeInbound($query)
    {
        return $query->where('direction', 'inbound');
    }

    /**
     * Scope for outbound messages.
     */
    public function scopeOutbound($query)
    {
        return $query->where('direction', 'outbound');
    }

    /**
     * Scope for delivered messages.
     */
    public function scopeDelivered($query)
    {
        return $query->where('delivery_status', 'delivered');
    }

    /**
     * Scope for read messages.
     */
    public function scopeRead($query)
    {
        return $query->where('delivery_status', 'read');
    }

    /**
     * Scope for failed messages.
     */
    public function scopeFailed($query)
    {
        return $query->where('delivery_status', 'failed');
    }

    /**
     * Scope for messages by type.
     */
    public function scopeByType($query, string $type)
    {
        return $query->where('message_type', $type);
    }
}
