<?php

namespace Crater\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class TaxiDriver extends Model
{
    use HasFactory;

    protected $fillable = [
        'tenant_id',
        'driver_number',
        'name',
        'phone',
        'email',
        'license_number',
        'license_expiry',
        'vehicle_make',
        'vehicle_model',
        'vehicle_year',
        'vehicle_color',
        'license_plate',
        'vehicle_capacity',
        'current_latitude',
        'current_longitude',
        'status',
        'is_available',
        'shift_start',
        'shift_end',
        'total_earnings',
        'total_trips',
        'average_rating',
        'last_location_update',
        'documents',
        'notes',
    ];

    protected $casts = [
        'license_expiry' => 'date',
        'current_latitude' => 'decimal:8',
        'current_longitude' => 'decimal:8',
        'is_available' => 'boolean',
        'shift_start' => 'datetime',
        'shift_end' => 'datetime',
        'total_earnings' => 'decimal:2',
        'average_rating' => 'decimal:2',
        'last_location_update' => 'datetime',
        'documents' => 'array',
    ];

    /**
     * Get the tenant that owns the driver.
     */
    public function tenant(): BelongsTo
    {
        return $this->belongsTo(Tenant::class);
    }

    /**
     * Get the bookings for this driver.
     */
    public function bookings(): HasMany
    {
        return $this->hasMany(TaxiBooking::class, 'driver_id');
    }

    /**
     * Get active bookings for this driver.
     */
    public function activeBookings(): HasMany
    {
        return $this->hasMany(TaxiBooking::class, 'driver_id')->active();
    }

    /**
     * Get completed bookings for this driver.
     */
    public function completedBookings(): HasMany
    {
        return $this->hasMany(TaxiBooking::class, 'driver_id')->completed();
    }

    /**
     * Check if driver is available.
     */
    public function isAvailable(): bool
    {
        return $this->is_available && $this->status === 'active';
    }

    /**
     * Check if driver is online.
     */
    public function isOnline(): bool
    {
        return $this->status === 'online';
    }

    /**
     * Check if driver is on shift.
     */
    public function isOnShift(): bool
    {
        $now = now();
        return $this->shift_start && $this->shift_end && 
               $now->between($this->shift_start, $this->shift_end);
    }

    /**
     * Set driver as available.
     */
    public function setAvailable(): void
    {
        $this->update([
            'is_available' => true,
            'status' => 'online',
        ]);
    }

    /**
     * Set driver as unavailable.
     */
    public function setUnavailable(): void
    {
        $this->update([
            'is_available' => false,
            'status' => 'offline',
        ]);
    }

    /**
     * Update driver location.
     */
    public function updateLocation(float $latitude, float $longitude): void
    {
        $this->update([
            'current_latitude' => $latitude,
            'current_longitude' => $longitude,
            'last_location_update' => now(),
        ]);
    }

    /**
     * Start shift.
     */
    public function startShift(): void
    {
        $this->update([
            'shift_start' => now(),
            'status' => 'online',
            'is_available' => true,
        ]);
    }

    /**
     * End shift.
     */
    public function endShift(): void
    {
        $this->update([
            'shift_end' => now(),
            'status' => 'offline',
            'is_available' => false,
        ]);
    }

    /**
     * Update earnings after completed trip.
     */
    public function updateEarnings(float $tripEarnings): void
    {
        $this->increment('total_trips');
        $this->increment('total_earnings', $tripEarnings);
    }

    /**
     * Update rating after trip feedback.
     */
    public function updateRating(int $newRating): void
    {
        $totalRatings = $this->total_trips;
        if ($totalRatings > 0) {
            $currentTotal = $this->average_rating * ($totalRatings - 1);
            $newAverage = ($currentTotal + $newRating) / $totalRatings;
            $this->update(['average_rating' => round($newAverage, 2)]);
        } else {
            $this->update(['average_rating' => $newRating]);
        }
    }

    /**
     * Get current coordinates.
     */
    public function getCurrentCoordinates(): array
    {
        return [
            'latitude' => $this->current_latitude,
            'longitude' => $this->current_longitude,
        ];
    }

    /**
     * Calculate distance from given coordinates.
     */
    public function distanceFrom(float $latitude, float $longitude): float
    {
        if (!$this->current_latitude || !$this->current_longitude) {
            return 0;
        }

        // Haversine formula for distance calculation
        $earthRadius = 6371; // km

        $latDelta = deg2rad($latitude - $this->current_latitude);
        $lonDelta = deg2rad($longitude - $this->current_longitude);

        $a = sin($latDelta / 2) * sin($latDelta / 2) +
             cos(deg2rad($this->current_latitude)) * cos(deg2rad($latitude)) *
             sin($lonDelta / 2) * sin($lonDelta / 2);

        $c = 2 * atan2(sqrt($a), sqrt(1 - $a));

        return $earthRadius * $c;
    }

    /**
     * Check if license is expiring soon.
     */
    public function isLicenseExpiringSoon(int $days = 30): bool
    {
        if (!$this->license_expiry) {
            return false;
        }

        return $this->license_expiry->diffInDays(now()) <= $days;
    }

    /**
     * Get document by type.
     */
    public function getDocument(string $type, $default = null)
    {
        return data_get($this->documents, $type, $default);
    }

    /**
     * Set document.
     */
    public function setDocument(string $type, array $document): void
    {
        $documents = $this->documents ?? [];
        $documents[$type] = $document;
        $this->documents = $documents;
        $this->save();
    }

    /**
     * Scope for available drivers.
     */
    public function scopeAvailable($query)
    {
        return $query->where('is_available', true)->where('status', 'online');
    }

    /**
     * Scope for online drivers.
     */
    public function scopeOnline($query)
    {
        return $query->where('status', 'online');
    }

    /**
     * Scope for drivers on shift.
     */
    public function scopeOnShift($query)
    {
        $now = now();
        return $query->where('shift_start', '<=', $now)
                    ->where('shift_end', '>=', $now);
    }

    /**
     * Scope for drivers near location.
     */
    public function scopeNearLocation($query, float $latitude, float $longitude, float $radiusKm = 10)
    {
        // This is a simplified version. In production, you'd use spatial queries
        return $query->whereNotNull('current_latitude')
                    ->whereNotNull('current_longitude');
    }
}
