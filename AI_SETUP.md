# Innovars AI Agents Systeem - Setup Gids

## Overzicht
Deze configuratie stelt je in staat om GitHub Copilot te vervangen door alternatieve AI providers zoals OpenRouter, Ollama en LM Studio.

## Vereisten

### 1. VS Code Extensions
Installeer de volgende extensions:
```bash
# Continue.dev - Alternatief voor GitHub Copilot
code --install-extension continue.continue

# Optioneel: GitHub Copilot (voor vergelijking)
code --install-extension github.copilot
code --install-extension github.copilot-chat
```

### 2. Ollama Setup
```bash
# Installeer Ollama
curl -fsSL https://ollama.ai/install.sh | sh

# Download modellen
ollama pull codellama:7b
ollama pull llama2:7b
ollama pull nomic-embed-text

# Start Ollama server
ollama serve
```

### 3. LM Studio Setup
1. Download LM Studio van https://lmstudio.ai/
2. Start de applicatie
3. Download een model (bijv. CodeLlama)
4. Start de local server op poort 1234

### 4. OpenRouter Setup
1. Ga naar https://openrouter.ai/
2. Maak een account aan
3. Verkrijg je API key van https://openrouter.ai/keys
4. Kopieer `.env.ai` naar `.env.ai.local`
5. Vul je API key in

## Configuratie

### Environment Variables
```bash
# Kopieer en bewerk het environment bestand
cp .env.ai .env.ai.local

# Bewerk .env.ai.local en vul je API keys in
nano .env.ai.local
```

### VS Code Tasks
De volgende tasks zijn beschikbaar via `Ctrl+Shift+P` > "Tasks: Run Task":

- **Update task labels en configuratie voor het Innovars AI Agents systeem**
- **Start Ollama Server** - Start Ollama automatisch
- **Start LM Studio Server** - Test LM Studio verbinding
- **Test OpenRouter Connection** - Test OpenRouter API
- **Laravel: Serve** - Start Laravel development server
- **Vite: Dev Server** - Start Vite development server

## Gebruik

### Continue.dev
1. Open een bestand in VS Code
2. Selecteer code
3. Druk op `Ctrl+I` voor inline editing
4. Druk op `Ctrl+Shift+I` voor chat interface
5. Gebruik `/` voor slash commands:
   - `/edit` - Bewerk geselecteerde code
   - `/comment` - Voeg comments toe
   - `/test` - Genereer tests
   - `/docs` - Genereer documentatie

### Custom Commands
- `test` - Schrijf uitgebreide tests
- `docs` - Schrijf documentatie
- `optimize` - Optimaliseer code
- `explain` - Leg code uit

### Model Switching
Je kunt switchen tussen modellen in Continue.dev:
- OpenRouter (Claude 3, GPT-4)
- Ollama (CodeLlama, Llama2)
- LM Studio (lokale modellen)

## Troubleshooting

### Ollama Issues
```bash
# Check of Ollama draait
curl http://localhost:11434/api/tags

# Herstart Ollama
pkill ollama
ollama serve
```

### LM Studio Issues
1. Zorg dat de server draait in LM Studio
2. Check poort 1234: `curl http://localhost:1234/v1/models`

### OpenRouter Issues
1. Controleer je API key
2. Check je credit balance op OpenRouter
3. Controleer rate limits

## Voordelen van deze Setup

1. **Privacy** - Lokale modellen (Ollama, LM Studio)
2. **Flexibiliteit** - Meerdere providers
3. **Cost Control** - Kies goedkopere modellen
4. **Offline Capability** - Werk zonder internet
5. **Customization** - Eigen modellen en prompts

## Aanbevolen Workflow

1. **Development**: Gebruik Ollama CodeLlama voor snelle autocomplete
2. **Complex Tasks**: Gebruik OpenRouter Claude 3 voor complexe problemen
3. **Privacy Sensitive**: Gebruik LM Studio voor gevoelige code
4. **Testing**: Gebruik custom commands voor test generatie

## Support

Voor vragen over deze configuratie, check:
- Continue.dev documentatie: https://continue.dev/docs
- Ollama documentatie: https://ollama.ai/docs
- OpenRouter documentatie: https://openrouter.ai/docs
