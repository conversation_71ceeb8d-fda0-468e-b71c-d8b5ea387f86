{"models": [{"title": "OpenRouter - Claude 3 Sonnet", "provider": "openrouter", "model": "anthropic/claude-3-sonnet", "apiKey": "${OPENROUTER_API_KEY}", "apiBase": "https://openrouter.ai/api/v1"}, {"title": "OpenRouter - GPT-4", "provider": "openrouter", "model": "openai/gpt-4", "apiKey": "${OPENROUTER_API_KEY}", "apiBase": "https://openrouter.ai/api/v1"}, {"title": "Ollama - CodeLlama", "provider": "ollama", "model": "codellama:7b", "apiBase": "http://localhost:11434"}, {"title": "Ollama - Llama 2", "provider": "ollama", "model": "llama2:7b", "apiBase": "http://localhost:11434"}, {"title": "LM Studio", "provider": "lmstudio", "model": "local-model", "apiBase": "http://localhost:1234/v1"}], "tabAutocompleteModel": {"title": "Ollama - CodeLlama Autocomplete", "provider": "ollama", "model": "codellama:7b", "apiBase": "http://localhost:11434"}, "customCommands": [{"name": "test", "prompt": "Write a comprehensive test suite for the following code. Include unit tests, integration tests, and edge cases:\n\n{{{ input }}}\n\nMake sure to use the appropriate testing framework for this language."}, {"name": "docs", "prompt": "Write comprehensive documentation for the following code. Include parameter descriptions, return values, examples, and any important notes:\n\n{{{ input }}}"}, {"name": "optimize", "prompt": "Analyze the following code and suggest optimizations for performance, readability, and maintainability:\n\n{{{ input }}}"}, {"name": "explain", "prompt": "Explain how this code works step by step:\n\n{{{ input }}}"}], "contextProviders": [{"name": "diff", "params": {}}, {"name": "folder", "params": {"folders": ["app", "resources/scripts", "config"]}}, {"name": "codebase", "params": {}}], "slashCommands": [{"name": "edit", "description": "Edit selected code"}, {"name": "comment", "description": "Write comments for the selected code"}, {"name": "share", "description": "Export the current chat session"}, {"name": "cmd", "description": "Generate a shell command"}], "allowAnonymousTelemetry": false, "embeddingsProvider": {"provider": "ollama", "model": "nomic-embed-text", "apiBase": "http://localhost:11434"}}