#!/usr/bin/env python3
"""
Update globale VS Code tasks.json met AI-gerelateerde taken
"""

import json
import os
import shutil
from pathlib import Path
from datetime import datetime

def main():
    print("🔧 Updating global VS Code tasks...")
    
    # VS Code tasks pad
    vscode_user_dir = Path.home() / "Library" / "Application Support" / "Code" / "User"
    tasks_file = vscode_user_dir / "tasks.json"
    
    # Backup maken als bestand bestaat
    if tasks_file.exists():
        backup_file = tasks_file.with_suffix(f".json.backup-{datetime.now().strftime('%Y%m%d-%H%M%S')}")
        shutil.copy2(tasks_file, backup_file)
        print(f"📋 Backup gemaakt: {backup_file}")
        
        # Bestaande tasks laden
        with open(tasks_file, 'r', encoding='utf-8') as f:
            tasks = json.load(f)
    else:
        tasks = {
            "version": "2.0.0",
            "tasks": []
        }
    
    # <PERSON>-gerelateerde taken toevoegen
    ai_tasks = [
        {
            "label": "🤖 Start Ollama Server",
            "type": "shell",
            "command": "ollama",
            "args": ["serve"],
            "group": "build",
            "presentation": {
                "echo": True,
                "reveal": "always",
                "focus": False,
                "panel": "new"
            },
            "problemMatcher": [],
            "runOptions": {
                "runOn": "folderOpen"
            }
        },
        {
            "label": "🧠 Test OpenRouter Connection",
            "type": "shell",
            "command": "curl",
            "args": [
                "-X", "POST",
                "https://openrouter.ai/api/v1/chat/completions",
                "-H", "Authorization: Bearer ${env:OPENROUTER_API_KEY}",
                "-H", "Content-Type: application/json",
                "-d", '{"model": "anthropic/claude-3.5-sonnet", "messages": [{"role": "user", "content": "Hello from VS Code!"}], "max_tokens": 50}'
            ],
            "group": "test",
            "presentation": {
                "echo": True,
                "reveal": "always",
                "focus": False,
                "panel": "shared"
            },
            "problemMatcher": []
        },
        {
            "label": "🏠 Test LM Studio Connection",
            "type": "shell",
            "command": "curl",
            "args": [
                "-X", "GET",
                "http://localhost:1234/v1/models"
            ],
            "group": "test",
            "presentation": {
                "echo": True,
                "reveal": "always",
                "focus": False,
                "panel": "shared"
            },
            "problemMatcher": []
        },
        {
            "label": "🦙 Test Ollama Connection",
            "type": "shell",
            "command": "curl",
            "args": [
                "http://localhost:11434/api/tags"
            ],
            "group": "test",
            "presentation": {
                "echo": True,
                "reveal": "always",
                "focus": False,
                "panel": "shared"
            },
            "problemMatcher": []
        },
        {
            "label": "📥 Download Ollama Models",
            "type": "shell",
            "command": "bash",
            "args": [
                "-c",
                "ollama pull codellama:7b && ollama pull codellama:13b && ollama pull deepseek-coder:6.7b && ollama pull llama3.1:8b && ollama pull nomic-embed-text"
            ],
            "group": "build",
            "presentation": {
                "echo": True,
                "reveal": "always",
                "focus": False,
                "panel": "new"
            },
            "problemMatcher": []
        },
        {
            "label": "🔄 Restart Continue.dev",
            "type": "shell",
            "command": "code",
            "args": [
                "--command",
                "continue.restartContinueServer"
            ],
            "group": "build",
            "presentation": {
                "echo": True,
                "reveal": "silent",
                "focus": False,
                "panel": "shared"
            },
            "problemMatcher": []
        },
        {
            "label": "⚙️ Open Continue.dev Config",
            "type": "shell",
            "command": "code",
            "args": [
                "${env:HOME}/.continue/config.json"
            ],
            "group": "build",
            "presentation": {
                "echo": True,
                "reveal": "silent",
                "focus": False,
                "panel": "shared"
            },
            "problemMatcher": []
        },
        {
            "label": "🔑 Edit AI Config",
            "type": "shell",
            "command": "code",
            "args": [
                "${env:HOME}/.ai-config"
            ],
            "group": "build",
            "presentation": {
                "echo": True,
                "reveal": "silent",
                "focus": False,
                "panel": "shared"
            },
            "problemMatcher": []
        }
    ]
    
    # Check welke taken al bestaan
    existing_labels = {task.get("label", "") for task in tasks.get("tasks", [])}
    
    # Alleen nieuwe taken toevoegen
    new_tasks = [task for task in ai_tasks if task["label"] not in existing_labels]
    
    # Taken toevoegen
    tasks["tasks"].extend(new_tasks)
    
    # Opslaan
    with open(tasks_file, 'w', encoding='utf-8') as f:
        json.dump(tasks, f, indent=4, ensure_ascii=False)
    
    print(f"✅ {len(new_tasks)} nieuwe AI-taken toegevoegd aan globale tasks.json")
    print("📋 Nieuwe taken:")
    for task in new_tasks:
        print(f"  • {task['label']}")

if __name__ == "__main__":
    main()
