#!/bin/bash

# Innovars AI Agents System - Eenvoudige Globale Installatie
# Dit script installeert alles wat je nodig hebt voor AI-ondersteuning in VS Code

set -e

echo "🚀 Innovars AI Agents System - Globale Installatie"
echo "=================================================="

# Kleuren
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 1. Python scripts uitvoeren
echo -e "${YELLOW}🔧 VS Code settings updaten...${NC}"
python3 update-vscode-settings.py

echo -e "${YELLOW}📋 Globale tasks updaten...${NC}"
python3 update-global-tasks.py

# 2. Continue.dev configuratie
echo -e "${YELLOW}⚙️ Continue.dev configuratie maken...${NC}"
mkdir -p "$HOME/.continue"

cat > "$HOME/.continue/config.json" << 'EOF'
{
  "models": [
    {
      "title": "OpenRouter - Claude 3.5 Sonnet",
      "provider": "openrouter",
      "model": "anthropic/claude-3.5-sonnet",
      "apiKey": "${OPENROUTER_API_KEY}",
      "apiBase": "https://openrouter.ai/api/v1"
    },
    {
      "title": "OpenRouter - GPT-4o",
      "provider": "openrouter", 
      "model": "openai/gpt-4o",
      "apiKey": "${OPENROUTER_API_KEY}",
      "apiBase": "https://openrouter.ai/api/v1"
    },
    {
      "title": "Ollama - CodeLlama",
      "provider": "ollama",
      "model": "codellama:13b",
      "apiBase": "http://localhost:11434"
    },
    {
      "title": "Ollama - DeepSeek Coder",
      "provider": "ollama",
      "model": "deepseek-coder:6.7b",
      "apiBase": "http://localhost:11434"
    },
    {
      "title": "LM Studio",
      "provider": "lmstudio",
      "model": "local-model",
      "apiBase": "http://localhost:1234/v1"
    }
  ],
  "tabAutocompleteModel": {
    "title": "Ollama - CodeLlama",
    "provider": "ollama",
    "model": "codellama:7b",
    "apiBase": "http://localhost:11434"
  },
  "customCommands": [
    {
      "name": "test",
      "prompt": "Write comprehensive tests for this code:\n\n{{{ input }}}"
    },
    {
      "name": "docs",
      "prompt": "Write documentation for this code:\n\n{{{ input }}}"
    },
    {
      "name": "optimize",
      "prompt": "Optimize this code:\n\n{{{ input }}}"
    },
    {
      "name": "explain",
      "prompt": "Explain this code:\n\n{{{ input }}}"
    }
  ],
  "allowAnonymousTelemetry": false
}
EOF

# 3. Environment configuratie
echo -e "${YELLOW}🔑 Environment configuratie maken...${NC}"
cat > "$HOME/.ai-config" << 'EOF'
# Innovars AI Agents System - Global Configuration
# Bewerk dit bestand en vul je eigen API keys in

# OpenRouter API Key (verkrijg van: https://openrouter.ai/keys)
export OPENROUTER_API_KEY="your_openrouter_api_key_here"

# Ollama Configuration
export OLLAMA_BASE_URL="http://localhost:11434"

# LM Studio Configuration  
export LMSTUDIO_BASE_URL="http://localhost:1234/v1"
EOF

# 4. VS Code extensions installeren
echo -e "${YELLOW}🔌 VS Code extensions installeren...${NC}"
code --install-extension continue.continue --force
echo -e "${GREEN}✅ Continue.dev geïnstalleerd${NC}"

# 5. Shell configuratie updaten
echo -e "${YELLOW}🐚 Shell configuratie updaten...${NC}"
SHELL_CONFIG=""
if [[ "$SHELL" == *"zsh"* ]]; then
    SHELL_CONFIG="$HOME/.zshrc"
elif [[ "$SHELL" == *"bash"* ]]; then
    SHELL_CONFIG="$HOME/.bashrc"
fi

if [[ -n "$SHELL_CONFIG" ]]; then
    if ! grep -q ".ai-config" "$SHELL_CONFIG" 2>/dev/null; then
        echo "" >> "$SHELL_CONFIG"
        echo "# Innovars AI Agents System" >> "$SHELL_CONFIG"
        echo "source ~/.ai-config 2>/dev/null || true" >> "$SHELL_CONFIG"
        echo -e "${GREEN}✅ Shell configuratie bijgewerkt${NC}"
    fi
fi

echo ""
echo -e "${GREEN}🎉 Installatie voltooid!${NC}"
echo ""
echo -e "${BLUE}📋 Volgende stappen:${NC}"
echo -e "1. Bewerk ~/.ai-config en vul je OpenRouter API key in:"
echo -e "   ${YELLOW}nano ~/.ai-config${NC}"
echo -e "2. Herstart je terminal en VS Code"
echo -e "3. Open VS Code en druk Ctrl+Shift+I voor Continue.dev"
echo ""
echo -e "${BLUE}🔧 Optioneel - Ollama installeren:${NC}"
echo -e "   ${YELLOW}curl -fsSL https://ollama.ai/install.sh | sh${NC}"
echo -e "   ${YELLOW}ollama pull codellama:7b${NC}"
echo ""
echo -e "${BLUE}💡 Tips:${NC}"
echo -e "• Gebruik Ctrl+I voor inline editing"
echo -e "• Gebruik Ctrl+Shift+I voor chat"
echo -e "• Gebruik Ctrl+Shift+P > 'Tasks: Run Task' voor AI taken"
echo -e "• Switch tussen modellen in Continue.dev"
