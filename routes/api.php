<?php

use Crater\Http\Controllers\AppVersionController;
use Crater\Http\Controllers\V1\Admin\Auth\ForgotPasswordController;
use Crater\Http\Controllers\V1\Admin\Auth\ResetPasswordController;
use Crater\Http\Controllers\V1\Admin\Dashboard\DashboardController;
use Crater\Http\Controllers\V1\Admin\TenantController;
use Crater\Http\Controllers\V1\Admin\General\BootstrapController;
use Crater\Http\Controllers\V1\Admin\General\CountriesController;
use Crater\Http\Controllers\V1\Admin\Mobile\AuthController;
use Crater\Http\Controllers\V1\Installation\AppDomainController;
use Crater\Http\Controllers\V1\Installation\DatabaseConfigurationController;
use Crater\Http\Controllers\V1\Installation\FilePermissionsController;
use Crater\Http\Controllers\V1\Installation\FinishController;
use Crater\Http\Controllers\V1\Installation\LoginController;
use Crater\Http\Controllers\V1\Installation\OnboardingWizardController;
use Crater\Http\Controllers\V1\Installation\RequirementsController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/


// ping
//----------------------------------

Route::get('ping', function () {
    return response()->json([
        'success' => 'crater-self-hosted',
    ]);
})->name('ping');


// Version 1 endpoints
// --------------------------------------
Route::prefix('/v1')->group(function () {


    // App version
    // ----------------------------------

    Route::get('/app/version', AppVersionController::class);


    // Authentication & Password Reset
    //----------------------------------

    Route::group(['prefix' => 'auth'], function () {
        Route::post('login', [AuthController::class, 'login']);

        Route::post('logout', [AuthController::class, 'logout'])->middleware('auth:sanctum');

        // Send reset password mail
        Route::post('password/email', [ForgotPasswordController::class, 'sendResetLinkEmail'])->middleware("throttle:10,2");

        // handle reset password form process
        Route::post('reset/password', [ResetPasswordController::class, 'reset']);
    });


    // Countries
    //----------------------------------

    Route::get('/countries', CountriesController::class);


    // Onboarding
    //----------------------------------

    Route::middleware(['redirect-if-installed'])->prefix('installation')->group(function () {
        Route::get('/wizard-step', [OnboardingWizardController::class, 'getStep']);

        Route::post('/wizard-step', [OnboardingWizardController::class, 'updateStep']);

        Route::get('/requirements', [RequirementsController::class, 'requirements']);

        Route::get('/permissions', [FilePermissionsController::class, 'permissions']);

        Route::post('/database/config', [DatabaseConfigurationController::class, 'saveDatabaseEnvironment']);

        Route::get('/database/config', [DatabaseConfigurationController::class, 'getDatabaseEnvironment']);

        Route::put('/set-domain', AppDomainController::class);

        Route::post('/login', LoginController::class);

        Route::post('/finish', FinishController::class);
    });


    Route::middleware(['auth:sanctum'])->group(function () {

        // Bootstrap
        //----------------------------------

        Route::get('/bootstrap', BootstrapController::class);

        // Dashboard
        //----------------------------------

        Route::get('/dashboard', DashboardController::class);

        // Auth check
        //----------------------------------

        Route::get('/auth/check', [AuthController::class, 'check']);

        // Tenant Management (Super Admin Only)
        //----------------------------------

        Route::prefix('tenants')->group(function () {
            Route::get('/', [TenantController::class, 'index']);
            Route::post('/', [TenantController::class, 'store']);
            Route::get('/business-types', [TenantController::class, 'businessTypes']);
            Route::get('/statistics', [TenantController::class, 'statistics']);
            Route::get('/{tenant}', [TenantController::class, 'show']);
            Route::put('/{tenant}', [TenantController::class, 'update']);
            Route::delete('/{tenant}', [TenantController::class, 'destroy']);
            Route::put('/{tenant}/settings', [TenantController::class, 'updateSettings']);
            Route::post('/{tenant}/activate', [TenantController::class, 'activate']);
            Route::post('/{tenant}/suspend', [TenantController::class, 'suspend']);
    });
});
