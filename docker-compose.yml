

services:
  app:
    build:
      args:
        user: crater-user
        uid: 1000
      context: ./
      dockerfile: Dockerfile
    image: crater-php
    restart: unless-stopped
    working_dir: /var/www/
    volumes:
      - ./:/var/www
      - ./docker-compose/php/uploads.ini:/usr/local/etc/php/conf.d/uploads.ini:rw,delegated
    networks:
      - crater

  db:
    image: mariadb
    restart: always
    volumes:
      - db:/var/lib/mysql
      # If you want to persist data on the host, comment the line above this one...
      # and uncomment the line under this one.
      #- ./docker-compose/db/data:/var/lib/mysql:rw,delegated
    environment:
      MYSQL_USER: crater
      MYSQL_PASSWORD: crater
      MYSQL_DATABASE: crater
      MYSQL_ROOT_PASSWORD: crater
    ports:
      - '33006:3306'
    networks:
      - crater

  nginx:
    image: nginx:1.17-alpine
    restart: unless-stopped
    ports:
      - 80:80
    volumes:
      - ./:/var/www
      - ./docker-compose/nginx:/etc/nginx/conf.d/
    networks:
      - crater

  cron:
    build:
      context: ./
      dockerfile: ./docker-compose/cron.dockerfile
    volumes:
      - ./:/var/www
    networks:
      - crater

volumes:
  db:

networks:
  crater:
    driver: bridge
