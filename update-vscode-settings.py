#!/usr/bin/env python3
"""
Innovars AI Agents System - VS Code Settings Updater
Dit script update je bestaande VS Code settings met AI orchestrator configuratie
"""

import json
import os
import shutil
from pathlib import Path
from datetime import datetime

def main():
    print("🚀 Innovars AI Agents System - VS Code Settings Update")
    print("=" * 55)
    
    # VS Code settings pad
    vscode_user_dir = Path.home() / "Library" / "Application Support" / "Code" / "User"
    settings_file = vscode_user_dir / "settings.json"
    
    if not settings_file.exists():
        print(f"❌ VS Code settings niet gevonden: {settings_file}")
        return
    
    # Backup maken
    backup_file = settings_file.with_suffix(f".json.backup-{datetime.now().strftime('%Y%m%d-%H%M%S')}")
    shutil.copy2(settings_file, backup_file)
    print(f"📋 Backup gemaakt: {backup_file}")
    
    # Bestaande settings laden
    try:
        with open(settings_file, 'r', encoding='utf-8') as f:
            content = f.read()
            
        # Fix voor dubbele JSON objecten (zoals in jouw bestand)
        if content.count('{') > 1 and content.rstrip().endswith('}{'):
            # Split en merge de JSON objecten
            parts = content.split('}{')
            if len(parts) == 2:
                first_json = parts[0] + '}'
                second_json = '{' + parts[1]
                
                settings1 = json.loads(first_json)
                settings2 = json.loads(second_json)
                
                # Merge de settings
                settings = {**settings1, **settings2}
            else:
                settings = json.loads(content)
        else:
            settings = json.loads(content)
            
    except json.JSONDecodeError as e:
        print(f"❌ Fout bij het lezen van settings.json: {e}")
        return
    
    # AI configuratie toevoegen/updaten
    ai_settings = {
        # Continue.dev configuratie
        "continue.telemetryEnabled": False,
        "continue.enableTabAutocomplete": True,
        
        # AI Orchestrator configuratie
        "ai.orchestrator.enabled": True,
        "ai.orchestrator.defaultProvider": "continue",
        
        # Enhanced Copilot configuratie
        "github.copilot.advanced": {
            **settings.get("github.copilot.advanced", {}),
            "length": 1000,
            "temperature": 0.1,
            "top_p": 0.95,
            "inlineSuggestCount": 5
        },
        
        # Editor configuratie voor AI
        "editor.inlineSuggest.enabled": True,
        "editor.suggest.preview": True,
        "editor.acceptSuggestionOnCommitCharacter": False,
        "editor.acceptSuggestionOnEnter": "on",
        "editor.suggestSelection": "first",
        "editor.tabCompletion": "on",
        "editor.wordBasedSuggestions": "matchingDocuments",
        "editor.quickSuggestions": {
            "other": True,
            "comments": True,
            "strings": True
        },
        
        # File associations voor AI
        "files.associations": {
            **settings.get("files.associations", {}),
            "*.ai": "markdown",
            "*.prompt": "markdown",
            ".continue/**/*.json": "jsonc"
        },
        
        # YAML schema voor Continue.dev
        "yaml.schemas": {
            **settings.get("yaml.schemas", {}),
            "https://raw.githubusercontent.com/continuedev/continue/main/schema/config.json": [
                ".continue/**/*.yaml",
                ".continue/**/*.yml"
            ]
        }
    }
    
    # Settings mergen
    settings.update(ai_settings)
    
    # Copilot instructies uitbreiden
    existing_instructions = settings.get("github.copilot.chat.codeGeneration.instructions", [])
    ai_instructions = [
        "Use Continue.dev for complex code generation when available",
        "Prefer local models (Ollama) for privacy-sensitive code",
        "Use OpenRouter models for complex reasoning tasks",
        "Always consider performance and security implications",
        "Follow language-specific best practices and conventions",
        "Implement proper error handling and logging",
        "Write self-documenting code with clear variable names",
        "Add comprehensive tests for all new functionality"
    ]
    
    # Merge instructies zonder duplicaten
    all_instructions = list(set(existing_instructions + ai_instructions))
    settings["github.copilot.chat.codeGeneration.instructions"] = all_instructions
    
    # Settings opslaan
    try:
        with open(settings_file, 'w', encoding='utf-8') as f:
            json.dump(settings, f, indent=2, ensure_ascii=False)
        
        print("✅ VS Code settings succesvol bijgewerkt!")
        print(f"📁 Settings bestand: {settings_file}")
        
        # Samenvatting van wijzigingen
        print("\n📋 Toegevoegde configuratie:")
        print("• Continue.dev telemetry uitgeschakeld")
        print("• AI orchestrator ingeschakeld")
        print("• Enhanced Copilot configuratie")
        print("• Verbeterde editor suggesties")
        print("• File associations voor AI bestanden")
        print("• YAML schema voor Continue.dev")
        print("• Uitgebreide Copilot instructies")
        
    except Exception as e:
        print(f"❌ Fout bij het opslaan: {e}")
        # Restore backup
        shutil.copy2(backup_file, settings_file)
        print("🔄 Backup hersteld")

if __name__ == "__main__":
    main()
