<template>\n  <!-- MOBILE MENU -->\n  <TransitionRoot as=\"template\" :show=\"globalStore.isSidebarOpen\">\n    <Dialog\n      as=\"div\"\n      class=\"fixed inset-0 z-40 flex md:hidden\"\n      @close=\"globalStore.setSidebarVisibility(false)\"\n    >\n      <TransitionChild\n        as=\"template\"\n        enter=\"transition-opacity ease-linear duration-300\"\n        enter-from=\"opacity-0\"\n        enter-to=\"opacity-100\"\n        leave=\"transition-opacity ease-linear duration-300\"\n        leave-from=\"opacity-100\"\n        leave-to=\"opacity-0\"\n      >\n        <DialogOverlay class=\"fixed inset-0 bg-gray-900 bg-opacity-50 backdrop-blur-sm\" />\n      </TransitionChild>\n\n      <TransitionChild\n        as=\"template\"\n        enter=\"transition ease-in-out duration-300\"\n        enter-from=\"-translate-x-full\"\n        enter-to=\"translate-x-0\"\n        leave=\"transition ease-in-out duration-300\"\n        leave-from=\"translate-x-0\"\n        leave-to=\"-translate-x-full\"\n      >\n        <div class=\"relative flex flex-col flex-1 w-full max-w-xs sidebar-modern\">\n          <TransitionChild\n            as=\"template\"\n            enter=\"ease-in-out duration-300\"\n            enter-from=\"opacity-0\"\n            enter-to=\"opacity-100\"\n            leave=\"ease-in-out duration-300\"\n            leave-from=\"opacity-100\"\n            leave-to=\"opacity-0\"\n          >\n            <div class=\"absolute top-0 right-0 pt-2 -mr-12\">\n              <button\n                class=\"flex items-center justify-center w-10 h-10 ml-1 rounded-full bg-white bg-opacity-20 backdrop-blur-sm hover:bg-opacity-30 transition-all duration-200\"\n                @click=\"globalStore.setSidebarVisibility(false)\"\n              >\n                <BaseIcon name=\"XIcon\" class=\"w-6 h-6 text-white\" />\n              </button>\n            </div>\n          </TransitionChild>\n          \n          <div class=\"flex-1 h-0 pt-5 pb-4 overflow-y-auto\">\n            <!-- Mobile Logo -->\n            <div class=\"flex items-center px-4 mb-8\">\n              <img\n                src=\"/img/innovars-logo-white.svg\"\n                class=\"h-8\"\n                alt=\"Innovars AI System CRM\"\n              />\n            </div>\n\n            <!-- Mobile Navigation -->\n            <nav class=\"px-2 space-y-1\">\n              <div v-for=\"menu in globalStore.menuGroups\" :key=\"menu\" class=\"space-y-1\">\n                <router-link\n                  v-for=\"item in menu\"\n                  :key=\"item.name\"\n                  :to=\"item.link\"\n                  :class=\"[\n                    hasActiveUrl(item.link)\n                      ? 'bg-white bg-opacity-20 text-white border-white'\n                      : 'text-white text-opacity-80 hover:bg-white hover:bg-opacity-10 border-transparent',\n                    'group flex items-center px-3 py-3 text-sm font-medium rounded-xl border-l-4 transition-all duration-200',\n                  ]\"\n                  @click=\"globalStore.setSidebarVisibility(false)\"\n                >\n                  <BaseIcon\n                    :name=\"item.icon\"\n                    :class=\"[\n                      hasActiveUrl(item.link)\n                        ? 'text-white'\n                        : 'text-white text-opacity-60 group-hover:text-white',\n                      'mr-3 h-5 w-5 transition-colors duration-200',\n                    ]\"\n                  />\n                  {{ $t(item.title) }}\n                </router-link>\n              </div>\n            </nav>\n          </div>\n        </div>\n      </TransitionChild>\n      <div class=\"shrink-0 w-14\"></div>\n    </Dialog>\n  </TransitionRoot>\n\n  <!-- DESKTOP MENU -->\n  <div class=\"hidden w-64 h-screen pb-32 overflow-y-auto sidebar-modern xl:w-72 md:fixed md:flex md:flex-col md:inset-y-0 pt-16\">\n    <!-- Sidebar Content -->\n    <div class=\"flex-1 px-4 py-6\">\n      <!-- User Welcome Section -->\n      <div class=\"mb-8 p-4 bg-white bg-opacity-10 rounded-2xl backdrop-blur-sm\">\n        <div class=\"flex items-center space-x-3\">\n          <img\n            :src=\"previewAvatar\"\n            class=\"w-10 h-10 rounded-xl object-cover border-2 border-white border-opacity-20\"\n            :alt=\"userStore.currentUser.first_name\"\n          />\n          <div>\n            <div class=\"text-white font-medium text-sm\">\n              Welcome back, {{ userStore.currentUser.first_name }}!\n            </div>\n            <div class=\"text-white text-opacity-70 text-xs\">\n              {{ getCurrentGreeting() }}\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Navigation Groups -->\n      <nav class=\"space-y-6\">\n        <div v-for=\"(menu, index) in globalStore.menuGroups\" :key=\"index\" class=\"space-y-1\">\n          <!-- Group Separator -->\n          <div v-if=\"index > 0\" class=\"border-t border-white border-opacity-10 pt-6\"></div>\n          \n          <router-link\n            v-for=\"item in menu\"\n            :key=\"item.name\"\n            :to=\"item.link\"\n            :class=\"[\n              hasActiveUrl(item.link)\n                ? 'bg-white bg-opacity-20 text-white shadow-lg transform scale-105'\n                : 'text-white text-opacity-80 hover:bg-white hover:bg-opacity-10 hover:text-white hover:transform hover:scale-105',\n              'group flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200 ease-out',\n            ]\"\n          >\n            <div\n              :class=\"[\n                hasActiveUrl(item.link)\n                  ? 'bg-white bg-opacity-20'\n                  : 'bg-white bg-opacity-10 group-hover:bg-opacity-20',\n                'p-2 rounded-lg mr-3 transition-all duration-200',\n              ]\"\n            >\n              <BaseIcon\n                :name=\"item.icon\"\n                :class=\"[\n                  hasActiveUrl(item.link)\n                    ? 'text-white'\n                    : 'text-white text-opacity-70 group-hover:text-white',\n                  'h-4 w-4 transition-colors duration-200',\n                ]\"\n              />\n            </div>\n            <span class=\"flex-1\">{{ $t(item.title) }}</span>\n            \n            <!-- Active indicator -->\n            <div\n              v-if=\"hasActiveUrl(item.link)\"\n              class=\"w-2 h-2 bg-white rounded-full\"\n            ></div>\n          </router-link>\n        </div>\n      </nav>\n    </div>\n\n    <!-- Bottom Section -->\n    <div class=\"px-4 pb-6\">\n      <!-- AI Assistant Quick Access -->\n      <div class=\"mb-4 p-4 bg-white bg-opacity-10 rounded-2xl backdrop-blur-sm\">\n        <div class=\"flex items-center space-x-3 mb-3\">\n          <div class=\"w-8 h-8 bg-white bg-opacity-20 rounded-lg flex items-center justify-center\">\n            <svg class=\"w-4 h-4 text-white\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path d=\"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z\"/>\n            </svg>\n          </div>\n          <div>\n            <div class=\"text-white font-medium text-sm\">AI Assistant</div>\n            <div class=\"text-white text-opacity-70 text-xs\">Get instant help</div>\n          </div>\n        </div>\n        <button class=\"w-full py-2 px-3 bg-white bg-opacity-20 hover:bg-opacity-30 text-white text-sm font-medium rounded-lg transition-all duration-200\">\n          Ask AI\n        </button>\n      </div>\n\n      <!-- Version Info -->\n      <div class=\"text-center text-white text-opacity-50 text-xs\">\n        Innovars AI System v2.0\n      </div>\n    </div>\n  </div>\n</template>\n\n<script setup>\nimport {\n  Dialog,\n  DialogOverlay,\n  TransitionChild,\n  TransitionRoot,\n} from '@headlessui/vue'\n\nimport { useRoute } from 'vue-router'\nimport { useGlobalStore } from '@/scripts/admin/stores/global'\nimport { useUserStore } from '@/scripts/admin/stores/user'\nimport { computed } from 'vue'\n\nconst route = useRoute()\nconst globalStore = useGlobalStore()\nconst userStore = useUserStore()\n\nconst previewAvatar = computed(() => {\n  return userStore.currentUser && userStore.currentUser.avatar !== 0\n    ? userStore.currentUser.avatar\n    : getDefaultAvatar()\n})\n\nfunction getDefaultAvatar() {\n  const imgUrl = new URL('/img/default-avatar.jpg', import.meta.url)\n  return imgUrl\n}\n\nfunction hasActiveUrl(url) {\n  return route.path.indexOf(url) > -1\n}\n\nfunction getCurrentGreeting() {\n  const hour = new Date().getHours()\n  if (hour < 12) return 'Good morning'\n  if (hour < 17) return 'Good afternoon'\n  return 'Good evening'\n}\n</script>\n\n<style scoped>\n.sidebar-modern {\n  background: linear-gradient(180deg, rgb(2, 132, 199) 0%, rgb(7, 89, 133) 100%);\n  backdrop-filter: blur(20px);\n}\n</style>