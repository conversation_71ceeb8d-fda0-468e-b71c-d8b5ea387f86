<template>\n  <header class=\"header-modern fixed top-0 left-0 z-20 flex items-center justify-between w-full px-4 py-3 md:h-16 md:px-8\">\n    <!-- Logo Section -->\n    <router-link\n      to=\"/admin/dashboard\"\n      class=\"flex items-center space-x-3 text-lg font-black tracking-wider text-gray-900 brand-main hidden md:block\"\n    >\n      <img \n        v-if=\"adminLogo\" \n        :src=\"adminLogo\" \n        class=\"h-8\" \n        alt=\"Innovars AI System CRM\"\n      />\n      <img \n        v-else\n        src=\"/img/innovars-logo.svg\" \n        class=\"h-8\" \n        alt=\"Innovars AI System CRM\"\n      />\n    </router-link>\n\n    <!-- Mobile Menu Toggle -->\n    <div\n      :class=\"{ 'is-active': globalStore.isSidebarOpen }\"\n      class=\"flex p-2 overflow-visible text-sm bg-white border border-gray-200 rounded-xl cursor-pointer md:hidden hover:bg-gray-50 shadow-sm\"\n      @click.prevent=\"onToggle\"\n    >\n      <BaseIcon name=\"MenuIcon\" class=\"!w-5 !h-5 text-gray-600\" />\n    </div>\n\n    <!-- Right Side Actions -->\n    <div class=\"flex items-center space-x-3\">\n      <!-- AI Assistant Button -->\n      <button \n        class=\"hidden md:flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-primary-500 to-secondary-500 text-white rounded-xl hover:shadow-lg transition-all duration-200\"\n        @click=\"toggleAIAssistant\"\n      >\n        <svg class=\"w-4 h-4\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path d=\"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z\"/>\n        </svg>\n        <span class=\"text-sm font-medium\">AI Assistant</span>\n      </button>\n\n      <!-- Quick Create Dropdown -->\n      <div v-if=\"hasCreateAbilities\" class=\"relative\">\n        <BaseDropdown width-class=\"w-56\">\n          <template #activator>\n            <div class=\"flex items-center justify-center w-10 h-10 bg-white border border-gray-200 rounded-xl hover:bg-gray-50 shadow-sm transition-all duration-200\">\n              <BaseIcon name=\"PlusIcon\" class=\"w-5 h-5 text-gray-600\" />\n            </div>\n          </template>\n\n          <div class=\"p-2\">\n            <div class=\"text-xs font-semibold text-gray-500 uppercase tracking-wide px-3 py-2\">Quick Create</div>\n            \n            <router-link to=\"/admin/invoices/create\">\n              <BaseDropdownItem v-if=\"userStore.hasAbilities(abilities.CREATE_INVOICE)\" class=\"rounded-lg\">\n                <div class=\"flex items-center space-x-3\">\n                  <div class=\"w-8 h-8 bg-primary-100 rounded-lg flex items-center justify-center\">\n                    <BaseIcon name=\"DocumentTextIcon\" class=\"w-4 h-4 text-primary-600\" />\n                  </div>\n                  <div>\n                    <div class=\"font-medium text-gray-900\">{{ $t('invoices.new_invoice') }}</div>\n                    <div class=\"text-xs text-gray-500\">Create a new invoice</div>\n                  </div>\n                </div>\n              </BaseDropdownItem>\n            </router-link>\n            \n            <router-link to=\"/admin/estimates/create\">\n              <BaseDropdownItem v-if=\"userStore.hasAbilities(abilities.CREATE_ESTIMATE)\" class=\"rounded-lg\">\n                <div class=\"flex items-center space-x-3\">\n                  <div class=\"w-8 h-8 bg-secondary-100 rounded-lg flex items-center justify-center\">\n                    <BaseIcon name=\"DocumentIcon\" class=\"w-4 h-4 text-secondary-600\" />\n                  </div>\n                  <div>\n                    <div class=\"font-medium text-gray-900\">{{ $t('estimates.new_estimate') }}</div>\n                    <div class=\"text-xs text-gray-500\">Create an estimate</div>\n                  </div>\n                </div>\n              </BaseDropdownItem>\n            </router-link>\n\n            <router-link to=\"/admin/customers/create\">\n              <BaseDropdownItem v-if=\"userStore.hasAbilities(abilities.CREATE_CUSTOMER)\" class=\"rounded-lg\">\n                <div class=\"flex items-center space-x-3\">\n                  <div class=\"w-8 h-8 bg-success-100 rounded-lg flex items-center justify-center\">\n                    <BaseIcon name=\"UserIcon\" class=\"w-4 h-4 text-success-600\" />\n                  </div>\n                  <div>\n                    <div class=\"font-medium text-gray-900\">{{ $t('customers.new_customer') }}</div>\n                    <div class=\"text-xs text-gray-500\">Add a new customer</div>\n                  </div>\n                </div>\n              </BaseDropdownItem>\n            </router-link>\n          </div>\n        </BaseDropdown>\n      </div>\n\n      <!-- Global Search -->\n      <div class=\"hidden md:block\">\n        <GlobalSearchBar\n          v-if=\"userStore.currentUser.is_owner || userStore.hasAbilities(abilities.VIEW_CUSTOMER)\"\n        />\n      </div>\n\n      <!-- Company Switcher -->\n      <CompanySwitcher />\n\n      <!-- Notifications -->\n      <button class=\"relative w-10 h-10 bg-white border border-gray-200 rounded-xl hover:bg-gray-50 shadow-sm transition-all duration-200\">\n        <BaseIcon name=\"BellIcon\" class=\"w-5 h-5 text-gray-600 mx-auto\" />\n        <span class=\"absolute -top-1 -right-1 w-3 h-3 bg-error-500 rounded-full\"></span>\n      </button>\n\n      <!-- User Profile Dropdown -->\n      <div class=\"relative\">\n        <BaseDropdown width-class=\"w-56\">\n          <template #activator>\n            <div class=\"flex items-center space-x-3 p-2 rounded-xl hover:bg-gray-50 transition-all duration-200\">\n              <img\n                :src=\"previewAvatar\"\n                class=\"w-8 h-8 rounded-lg object-cover border border-gray-200\"\n                :alt=\"userStore.currentUser.first_name\"\n              />\n              <div class=\"hidden md:block text-left\">\n                <div class=\"text-sm font-medium text-gray-900\">{{ userStore.currentUser.first_name }} {{ userStore.currentUser.last_name }}</div>\n                <div class=\"text-xs text-gray-500\">{{ userStore.currentUser.email }}</div>\n              </div>\n            </div>\n          </template>\n\n          <div class=\"p-2\">\n            <div class=\"px-3 py-2 border-b border-gray-100\">\n              <div class=\"font-medium text-gray-900\">{{ userStore.currentUser.first_name }} {{ userStore.currentUser.last_name }}</div>\n              <div class=\"text-sm text-gray-500\">{{ userStore.currentUser.email }}</div>\n            </div>\n            \n            <div class=\"py-2\">\n              <router-link to=\"/admin/settings/account-settings\">\n                <BaseDropdownItem class=\"rounded-lg\">\n                  <BaseIcon name=\"CogIcon\" class=\"w-4 h-4 mr-3 text-gray-400\" />\n                  {{ $t('navigation.settings') }}\n                </BaseDropdownItem>\n              </router-link>\n\n              <BaseDropdownItem @click=\"logout\" class=\"rounded-lg text-error-600 hover:bg-error-50\">\n                <BaseIcon name=\"LogoutIcon\" class=\"w-4 h-4 mr-3 text-error-500\" />\n                {{ $t('navigation.logout') }}\n              </BaseDropdownItem>\n            </div>\n          </div>\n        </BaseDropdown>\n      </div>\n    </div>\n  </header>\n</template>\n\n<script setup>\nimport { useAuthStore } from '@/scripts/admin/stores/auth'\nimport { useRouter } from 'vue-router'\nimport { computed, ref } from 'vue'\nimport { useUserStore } from '@/scripts/admin/stores/user'\nimport { useGlobalStore } from '@/scripts/admin/stores/global'\n\nimport CompanySwitcher from '@/scripts/components/CompanySwitcher.vue'\nimport GlobalSearchBar from '@/scripts/components/GlobalSearchBar.vue'\n\nimport abilities from '@/scripts/admin/stub/abilities'\n\nconst authStore = useAuthStore()\nconst userStore = useUserStore()\nconst globalStore = useGlobalStore()\nconst router = useRouter()\n\nconst showAIAssistant = ref(false)\n\nconst previewAvatar = computed(() => {\n  return userStore.currentUser && userStore.currentUser.avatar !== 0\n    ? userStore.currentUser.avatar\n    : getDefaultAvatar()\n})\n\nconst adminLogo = computed(() => {\n  if (globalStore.globalSettings.admin_portal_logo) {\n    return '/storage/' + globalStore.globalSettings.admin_portal_logo\n  }\n  return false\n})\n\nfunction getDefaultAvatar() {\n  const imgUrl = new URL('/img/default-avatar.jpg', import.meta.url)\n  return imgUrl\n}\n\nfunction hasCreateAbilities() {\n  return userStore.hasAbilities([\n    abilities.CREATE_INVOICE,\n    abilities.CREATE_ESTIMATE,\n    abilities.CREATE_CUSTOMER,\n  ])\n}\n\nfunction toggleAIAssistant() {\n  showAIAssistant.value = !showAIAssistant.value\n  // TODO: Implement AI Assistant modal/sidebar\n}\n\nasync function logout() {\n  await authStore.logout()\n  router.push('/login')\n}\n\nfunction onToggle() {\n  globalStore.setSidebarVisibility(true)\n}\n</script>\n\n<style scoped>\n.header-modern {\n  backdrop-filter: blur(20px);\n  background: rgba(255, 255, 255, 0.95);\n  border-bottom: 1px solid rgb(226, 232, 240);\n}\n</style>