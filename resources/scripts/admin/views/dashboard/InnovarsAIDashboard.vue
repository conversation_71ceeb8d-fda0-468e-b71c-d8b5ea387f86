<script setup>
import { ref, onMounted, computed } from 'vue'
import { useUserStore } from '@/scripts/admin/stores/user'
import { useRoute, useRouter } from 'vue-router'
import DashboardStats from './DashboardStats.vue'
import DashboardChart from './DashboardChart.vue'
import DashboardTable from './DashboardTable.vue'

const route = useRoute()
const userStore = useUserStore()
const router = useRouter()

// AI-powered insights
const aiInsights = ref([
  {
    id: 1,
    type: 'revenue',
    title: 'Revenue Trend Analysis',
    description: 'Your revenue has increased by 23% this month compared to last month.',
    confidence: 92,
    action: 'View detailed analysis',
    icon: '📈',
    color: 'success'
  },
  {
    id: 2,
    type: 'customers',
    title: 'Customer Behavior Insight',
    description: 'Top 3 customers contribute to 45% of your total revenue.',
    confidence: 87,
    action: 'Optimize customer relationships',
    icon: '👥',
    color: 'primary'
  },
  {
    id: 3,
    type: 'prediction',
    title: 'Cash Flow Prediction',
    description: 'Based on current trends, expect €15,000 in the next 30 days.',
    confidence: 78,
    action: 'View forecast details',
    icon: '🔮',
    color: 'secondary'
  }
])

const quickActions = ref([
  { name: 'Create Invoice', icon: '📄', route: 'invoices.create', color: 'primary' },
  { name: 'Add Customer', icon: '👤', route: 'customers.create', color: 'success' },
  { name: 'Record Payment', icon: '💰', route: 'payments.create', color: 'warning' },
  { name: 'Generate Report', icon: '📊', route: 'reports.sales', color: 'secondary' }
])

const currentTime = ref(new Date())

onMounted(() => {
  if (route.meta.ability && !userStore.hasAbilities(route.meta.ability)) {
    router.push({ name: 'account.settings' })
  } else if (route.meta.isOwner && !userStore.currentUser.is_owner) {
    router.push({ name: 'account.settings' })
  }
  
  // Update time every minute
  setInterval(() => {
    currentTime.value = new Date()
  }, 60000)
})

const greeting = computed(() => {
  const hour = currentTime.value.getHours()
  if (hour < 12) return 'Good Morning'
  if (hour < 17) return 'Good Afternoon'
  return 'Good Evening'
})

const formatTime = computed(() => {
  return currentTime.value.toLocaleString('nl-NL', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
})
</script>

<template>
  <BasePage>
    <!-- Modern Header with Greeting -->
    <div class="mb-8">
      <div class="card-modern p-6">
        <div class="flex items-center justify-between">
          <div>
            <h1 class="text-3xl font-bold text-gray-900 mb-2">
              {{ greeting }}, {{ userStore.currentUser.first_name }}! 👋
            </h1>
            <p class="text-gray-600">{{ formatTime }}</p>
            <p class="text-sm text-gray-500 mt-1">Welcome to your Innovars AI System CRM</p>
          </div>
          <div class="hidden md:block">
            <div class="gradient-primary p-4 rounded-2xl">
              <svg class="w-12 h-12 text-white" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
              </svg>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- AI Insights Section -->
    <div class="mb-8">
      <div class="flex items-center mb-4">
        <h2 class="text-xl font-semibold text-gray-900">🤖 AI Insights</h2>
        <span class="ml-2 px-2 py-1 text-xs bg-primary-100 text-primary-700 rounded-full">Powered by AI</span>
      </div>
      
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div 
          v-for="insight in aiInsights" 
          :key="insight.id"
          class="card-modern p-6 hover:scale-105 transition-transform duration-200"
        >
          <div class="flex items-start justify-between mb-4">
            <div class="text-2xl">{{ insight.icon }}</div>
            <div class="flex items-center">
              <div class="w-2 h-2 bg-success-400 rounded-full mr-2"></div>
              <span class="text-xs text-gray-500">{{ insight.confidence }}% confidence</span>
            </div>
          </div>
          
          <h3 class="font-semibold text-gray-900 mb-2">{{ insight.title }}</h3>
          <p class="text-sm text-gray-600 mb-4">{{ insight.description }}</p>
          
          <button 
            :class="`btn-${insight.color} text-sm py-2 px-4`"
            class="w-full"
          >
            {{ insight.action }}
          </button>
        </div>
      </div>
    </div>

    <!-- Quick Actions -->
    <div class="mb-8">
      <h2 class="text-xl font-semibold text-gray-900 mb-4">⚡ Quick Actions</h2>
      <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
        <router-link
          v-for="action in quickActions"
          :key="action.name"
          :to="{ name: action.route }"
          class="card-modern p-4 text-center hover:scale-105 transition-all duration-200 group"
        >
          <div class="text-2xl mb-2">{{ action.icon }}</div>
          <div class="text-sm font-medium text-gray-700 group-hover:text-primary-600">
            {{ action.name }}
          </div>
        </router-link>
      </div>
    </div>

    <!-- Traditional Dashboard Components with Modern Styling -->
    <div class="space-y-8">
      <div class="card-modern p-6">
        <h2 class="text-xl font-semibold text-gray-900 mb-4">📊 Business Overview</h2>
        <DashboardStats />
      </div>
      
      <div class="card-modern p-6">
        <h2 class="text-xl font-semibold text-gray-900 mb-4">📈 Revenue Analytics</h2>
        <DashboardChart />
      </div>
      
      <div class="card-modern p-6">
        <h2 class="text-xl font-semibold text-gray-900 mb-4">📋 Recent Activity</h2>
        <DashboardTable />
      </div>
    </div>

    <!-- AI Assistant Chat Button (Fixed Position) -->
    <div class="fixed bottom-6 right-6 z-50">
      <button class="gradient-primary p-4 rounded-full shadow-xl hover:shadow-2xl transform hover:scale-110 transition-all duration-200">
        <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
          <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
        </svg>
      </button>
    </div>
  </BasePage>
</template>

<style scoped>
.animate-fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

.animate-slide-up {
  animation: slideUp 0.3s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { 
    transform: translateY(10px); 
    opacity: 0; 
  }
  to { 
    transform: translateY(0); 
    opacity: 1; 
  }
}
</style>