<template>
  <div class="ai-dashboard">
    <!-- Header -->
    <div class="dashboard-header">
      <div class="header-gradient">
        <h1 class="dashboard-title">
          🤖 Innovars AI Agent Orchestrator Dashboard
        </h1>
        <p class="dashboard-subtitle">
          <PERSON>'s AI Multi-Agent Orchestration Control Center
        </p>
      </div>
    </div>

    <!-- Main Grid -->
    <div class="dashboard-grid">
      <!-- AI Agents Card -->
      <div class="dashboard-card">
        <div class="card-header">
          <h2 class="card-title">
            <span class="status-indicator running"></span>
            AI Agents
          </h2>
        </div>
        <div class="card-content">
          <p class="card-description">Manage and monitor active AI agents</p>
          <div class="button-grid">
            <button @click="startAgent" class="action-btn primary">
              Start Agent
            </button>
            <button @click="monitorAgents" class="action-btn secondary">
              Monitor
            </button>
            <button @click="stopAllAgents" class="action-btn danger">
              Stop All
            </button>
          </div>
        </div>
      </div>

      <!-- Task Scheduler Card -->
      <div class="dashboard-card">
        <div class="card-header">
          <h2 class="card-title">
            <span class="status-indicator running"></span>
            Task Scheduler
          </h2>
        </div>
        <div class="card-content">
          <p class="card-description">Schedule and manage automated tasks</p>
          <div class="button-grid">
            <button @click="createNewTask" class="action-btn primary">
              New Task
            </button>
            <button @click="viewQueue" class="action-btn secondary">
              View Queue
            </button>
            <button @click="viewHistory" class="action-btn secondary">
              History
            </button>
          </div>
        </div>
      </div>

      <!-- Memory Management Card -->
      <div class="dashboard-card">
        <div class="card-header">
          <h2 class="card-title">
            <span class="status-indicator running"></span>
            Memory Management
          </h2>
        </div>
        <div class="card-content">
          <p class="card-description">Monitor agent memory and knowledge base</p>
          <div class="button-grid">
            <button @click="optimizeMemory" class="action-btn primary">
              Optimize
            </button>
            <button @click="backupMemory" class="action-btn secondary">
              Backup
            </button>
            <button @click="clearMemory" class="action-btn danger">
              Clear
            </button>
          </div>
        </div>
      </div>

      <!-- API Configuration Card -->
      <div class="dashboard-card">
        <div class="card-header">
          <h2 class="card-title">
            <span class="status-indicator running"></span>
            API Configuration
          </h2>
        </div>
        <div class="card-content">
          <p class="card-description">Manage API keys and external services</p>
          <div class="button-grid">
            <button @click="configureAPI" class="action-btn primary">
              Configure
            </button>
            <button @click="testAPI" class="action-btn secondary">
              Test
            </button>
            <button @click="rotateKeys" class="action-btn secondary">
              Rotate
            </button>
          </div>
        </div>
      </div>

      <!-- Email Services Card -->
      <div class="dashboard-card">
        <div class="card-header">
          <h2 class="card-title">
            <span class="status-indicator warning"></span>
            Email Services
          </h2>
        </div>
        <div class="card-content">
          <p class="card-description">Automated email management and templates</p>
          <div class="button-grid">
            <button @click="configureEmail" class="action-btn primary">
              Configure
            </button>
            <button @click="sendTestEmail" class="action-btn secondary">
              Test Send
            </button>
            <button @click="viewEmailLogs" class="action-btn secondary">
              View Logs
            </button>
          </div>
        </div>
      </div>

      <!-- System Monitoring Card -->
      <div class="dashboard-card">
        <div class="card-header">
          <h2 class="card-title">
            <span class="status-indicator error"></span>
            System Monitoring
          </h2>
        </div>
        <div class="card-content">
          <p class="card-description">Real-time system health and performance</p>
          <div class="button-grid">
            <button @click="viewMetrics" class="action-btn primary">
              Metrics
            </button>
            <button @click="viewAlerts" class="action-btn secondary">
              Alerts
            </button>
            <button @click="systemStatus" class="action-btn secondary">
              Status
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- System Logs Panel -->
    <div class="logs-panel">
      <div class="logs-header">
        <h3>🔍 System Logs</h3>
        <button @click="refreshLogs" class="refresh-btn">Refresh</button>
      </div>
      <div class="logs-content">
        <div v-for="log in logs" :key="log.id" class="log-entry" :class="log.level">
          <span class="log-time">{{ formatTime(log.created_at) }}</span>
          <span class="log-level">{{ log.level.toUpperCase() }}</span>
          <span class="log-category">{{ log.category }}</span>
          <span class="log-message">{{ log.message }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import axios from 'axios'

export default {
  name: 'AIDashboard',
  setup() {
    const logs = ref([])
    const agents = ref([])
    const tasks = ref([])
    const stats = ref({})

    const loadDashboardData = async () => {
      try {
        const response = await axios.get('/api/ai-dashboard')
        logs.value = response.data.logs
        agents.value = response.data.agents
        tasks.value = response.data.tasks
        stats.value = response.data.stats
      } catch (error) {
        console.error('Failed to load dashboard data:', error)
      }
    }

    const formatTime = (timestamp) => {
      return new Date(timestamp).toLocaleTimeString()
    }

    // Action methods
    const startAgent = () => {
      console.log('Starting agent...')
    }

    const monitorAgents = () => {
      console.log('Monitoring agents...')
    }

    const stopAllAgents = async () => {
      try {
        await axios.post('/api/ai-dashboard/agents/stop-all')
        await loadDashboardData()
      } catch (error) {
        console.error('Failed to stop agents:', error)
      }
    }

    const createNewTask = () => {
      console.log('Creating new task...')
    }

    const viewQueue = () => {
      console.log('Viewing task queue...')
    }

    const viewHistory = () => {
      console.log('Viewing task history...')
    }

    const optimizeMemory = () => {
      console.log('Optimizing memory...')
    }

    const backupMemory = () => {
      console.log('Backing up memory...')
    }

    const clearMemory = () => {
      console.log('Clearing memory...')
    }

    const configureAPI = () => {
      console.log('Configuring API...')
    }

    const testAPI = () => {
      console.log('Testing API...')
    }

    const rotateKeys = () => {
      console.log('Rotating keys...')
    }

    const configureEmail = () => {
      console.log('Configuring email...')
    }

    const sendTestEmail = () => {
      console.log('Sending test email...')
    }

    const viewEmailLogs = () => {
      console.log('Viewing email logs...')
    }

    const viewMetrics = () => {
      console.log('Viewing metrics...')
    }

    const viewAlerts = () => {
      console.log('Viewing alerts...')
    }

    const systemStatus = () => {
      console.log('Checking system status...')
    }

    const refreshLogs = () => {
      loadDashboardData()
    }

    onMounted(() => {
      loadDashboardData()
      // Set up auto-refresh every 30 seconds
      setInterval(loadDashboardData, 30000)
    })

    return {
      logs,
      agents,
      tasks,
      stats,
      formatTime,
      startAgent,
      monitorAgents,
      stopAllAgents,
      createNewTask,
      viewQueue,
      viewHistory,
      optimizeMemory,
      backupMemory,
      clearMemory,
      configureAPI,
      testAPI,
      rotateKeys,
      configureEmail,
      sendTestEmail,
      viewEmailLogs,
      viewMetrics,
      viewAlerts,
      systemStatus,
      refreshLogs
    }
  }
}
</script>

<style scoped>
.ai-dashboard {
  min-height: 100vh;
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
  color: #ffffff;
  padding: 0;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

.dashboard-header {
  padding: 2rem 2rem 1rem;
}

.header-gradient {
  background: linear-gradient(135deg, #ff6b6b 0%, #4ecdc4 50%, #45b7d1 100%);
  padding: 2rem;
  border-radius: 12px;
  text-align: center;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.dashboard-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.dashboard-subtitle {
  font-size: 1.1rem;
  opacity: 0.9;
  margin: 0;
  font-weight: 400;
}

.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 1.5rem;
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;
}

.dashboard-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 1.5rem;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.dashboard-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.3);
  border-color: rgba(255, 255, 255, 0.2);
}

.card-header {
  margin-bottom: 1rem;
}

.card-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.status-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  display: inline-block;
}

.status-indicator.running {
  background: #4ade80;
  box-shadow: 0 0 8px rgba(74, 222, 128, 0.5);
}

.status-indicator.warning {
  background: #fbbf24;
  box-shadow: 0 0 8px rgba(251, 191, 36, 0.5);
}

.status-indicator.error {
  background: #ef4444;
  box-shadow: 0 0 8px rgba(239, 68, 68, 0.5);
}

.card-description {
  color: rgba(255, 255, 255, 0.7);
  margin: 0 0 1.5rem 0;
  font-size: 0.9rem;
}

.button-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  gap: 0.75rem;
}

.action-btn {
  padding: 0.75rem 1rem;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.action-btn.primary {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
}

.action-btn.primary:hover {
  background: linear-gradient(135deg, #2563eb, #1e40af);
  transform: translateY(-1px);
}

.action-btn.secondary {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.action-btn.secondary:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}

.action-btn.danger {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
}

.action-btn.danger:hover {
  background: linear-gradient(135deg, #dc2626, #b91c1c);
  transform: translateY(-1px);
}

.logs-panel {
  margin: 2rem;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 12px;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.logs-header {
  background: rgba(255, 255, 255, 0.05);
  padding: 1rem 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.logs-header h3 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
}

.refresh-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.875rem;
  transition: all 0.2s ease;
}

.refresh-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.logs-content {
  max-height: 300px;
  overflow-y: auto;
  padding: 1rem;
}

.log-entry {
  display: grid;
  grid-template-columns: auto auto auto 1fr;
  gap: 1rem;
  padding: 0.5rem 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  font-family: 'Monaco', 'Menlo', monospace;
  font-size: 0.875rem;
}

.log-entry:last-child {
  border-bottom: none;
}

.log-time {
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.8rem;
}

.log-level {
  font-weight: 600;
  padding: 0.2rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
}

.log-entry.info .log-level {
  background: rgba(59, 130, 246, 0.2);
  color: #60a5fa;
}

.log-entry.warning .log-level {
  background: rgba(251, 191, 36, 0.2);
  color: #fbbf24;
}

.log-entry.error .log-level {
  background: rgba(239, 68, 68, 0.2);
  color: #f87171;
}

.log-category {
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
}

.log-message {
  color: rgba(255, 255, 255, 0.9);
}

/* Responsive design */
@media (max-width: 768px) {
  .dashboard-grid {
    grid-template-columns: 1fr;
    padding: 1rem;
  }

  .dashboard-title {
    font-size: 2rem;
  }

  .button-grid {
    grid-template-columns: 1fr;
  }
}
</style>
