<template>\n  <div class=\"min-h-screen flex\">\n    <!-- Left Side - Login Form -->\n    <div class=\"flex-1 flex flex-col justify-center py-12 px-4 sm:px-6 lg:px-20 xl:px-24 bg-white\">\n      <div class=\"mx-auto w-full max-w-sm lg:w-96\">\n        <!-- Logo -->\n        <div class=\"mb-8\">\n          <img\n            src=\"/img/innovars-logo.svg\"\n            class=\"h-12 w-auto\"\n            alt=\"Innovars AI System CRM\"\n          />\n        </div>\n\n        <!-- Welcome Text -->\n        <div class=\"mb-8\">\n          <h2 class=\"text-3xl font-bold text-gray-900 mb-2\">\n            Welcome back! 👋\n          </h2>\n          <p class=\"text-gray-600\">\n            Sign in to your Innovars AI System CRM account\n          </p>\n        </div>\n\n        <!-- Login Form -->\n        <form class=\"space-y-6\" @submit.prevent=\"onSubmit\">\n          <!-- Email Field -->\n          <div>\n            <label for=\"email\" class=\"block text-sm font-medium text-gray-700 mb-2\">\n              {{ $t('login.email') }}\n            </label>\n            <div class=\"relative\">\n              <input\n                id=\"email\"\n                v-model=\"authStore.loginData.email\"\n                :class=\"[\n                  'input-modern',\n                  v$.email.$error ? 'border-error-500 focus:border-error-500 focus:ring-error-200' : ''\n                ]\"\n                type=\"email\"\n                name=\"email\"\n                autocomplete=\"email\"\n                placeholder=\"Enter your email address\"\n                @input=\"v$.email.$touch()\"\n              />\n              <div class=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                <svg class=\"h-5 w-5 text-gray-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207\" />\n                </svg>\n              </div>\n            </div>\n            <p v-if=\"v$.email.$error\" class=\"mt-1 text-sm text-error-600\">\n              {{ v$.email.$errors[0].$message }}\n            </p>\n          </div>\n\n          <!-- Password Field -->\n          <div>\n            <label for=\"password\" class=\"block text-sm font-medium text-gray-700 mb-2\">\n              {{ $t('login.password') }}\n            </label>\n            <div class=\"relative\">\n              <input\n                id=\"password\"\n                v-model=\"authStore.loginData.password\"\n                :class=\"[\n                  'input-modern pr-12',\n                  v$.password.$error ? 'border-error-500 focus:border-error-500 focus:ring-error-200' : ''\n                ]\"\n                :type=\"getInputType\"\n                name=\"password\"\n                autocomplete=\"current-password\"\n                placeholder=\"Enter your password\"\n                @input=\"v$.password.$touch()\"\n              />\n              <div class=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                <svg class=\"h-5 w-5 text-gray-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z\" />\n                </svg>\n              </div>\n              <button\n                type=\"button\"\n                class=\"absolute inset-y-0 right-0 pr-3 flex items-center\"\n                @click=\"isShowPassword = !isShowPassword\"\n              >\n                <svg v-if=\"isShowPassword\" class=\"h-5 w-5 text-gray-400 hover:text-gray-600\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L8.464 8.464M9.878 9.878l-1.415-1.414M14.12 14.12l1.415 1.415M14.12 14.12L15.536 15.536M14.12 14.12l1.414 1.414\" />\n                </svg>\n                <svg v-else class=\"h-5 w-5 text-gray-400 hover:text-gray-600\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M15 12a3 3 0 11-6 0 3 3 0 016 0z\" />\n                  <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z\" />\n                </svg>\n              </button>\n            </div>\n            <p v-if=\"v$.password.$error\" class=\"mt-1 text-sm text-error-600\">\n              {{ v$.password.$errors[0].$message }}\n            </p>\n          </div>\n\n          <!-- Remember Me & Forgot Password -->\n          <div class=\"flex items-center justify-between\">\n            <div class=\"flex items-center\">\n              <input\n                id=\"remember-me\"\n                name=\"remember-me\"\n                type=\"checkbox\"\n                class=\"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded\"\n              />\n              <label for=\"remember-me\" class=\"ml-2 block text-sm text-gray-700\">\n                Remember me\n              </label>\n            </div>\n\n            <router-link\n              to=\"forgot-password\"\n              class=\"text-sm text-primary-600 hover:text-primary-500 font-medium\"\n            >\n              {{ $t('login.forgot_password') }}\n            </router-link>\n          </div>\n\n          <!-- Submit Button -->\n          <button\n            type=\"submit\"\n            :disabled=\"isLoading\"\n            class=\"btn-primary w-full flex justify-center items-center space-x-2\"\n          >\n            <svg v-if=\"isLoading\" class=\"animate-spin -ml-1 mr-3 h-5 w-5 text-white\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\n              <circle class=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" stroke-width=\"4\"></circle>\n              <path class=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n            </svg>\n            <span>{{ isLoading ? 'Signing in...' : $t('login.login') }}</span>\n          </button>\n        </form>\n\n        <!-- AI Features Notice -->\n        <div class=\"mt-8 p-4 bg-gradient-to-r from-primary-50 to-secondary-50 rounded-xl border border-primary-100\">\n          <div class=\"flex items-center space-x-3\">\n            <div class=\"w-8 h-8 bg-gradient-to-r from-primary-500 to-secondary-500 rounded-lg flex items-center justify-center\">\n              <svg class=\"w-4 h-4 text-white\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path d=\"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z\"/>\n              </svg>\n            </div>\n            <div>\n              <h3 class=\"text-sm font-medium text-gray-900\">AI-Powered CRM</h3>\n              <p class=\"text-xs text-gray-600\">Get intelligent insights and automated workflows</p>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Right Side - Hero Section -->\n    <div class=\"hidden lg:block relative w-0 flex-1\">\n      <div class=\"absolute inset-0 gradient-primary\">\n        <!-- Background Pattern -->\n        <div class=\"absolute inset-0 opacity-10\">\n          <svg class=\"w-full h-full\" viewBox=\"0 0 100 100\" xmlns=\"http://www.w3.org/2000/svg\">\n            <defs>\n              <pattern id=\"grid\" width=\"10\" height=\"10\" patternUnits=\"userSpaceOnUse\">\n                <path d=\"M 10 0 L 0 0 0 10\" fill=\"none\" stroke=\"white\" stroke-width=\"0.5\"/>\n              </pattern>\n            </defs>\n            <rect width=\"100\" height=\"100\" fill=\"url(#grid)\" />\n          </svg>\n        </div>\n        \n        <!-- Content -->\n        <div class=\"relative h-full flex flex-col justify-center px-12\">\n          <div class=\"max-w-md\">\n            <h1 class=\"text-4xl font-bold text-white mb-6\">\n              Transform Your Business with AI\n            </h1>\n            <p class=\"text-xl text-white text-opacity-90 mb-8\">\n              Innovars AI System CRM helps you manage customers, automate workflows, and grow your business with intelligent insights.\n            </p>\n            \n            <!-- Features List -->\n            <div class=\"space-y-4\">\n              <div class=\"flex items-center space-x-3\">\n                <div class=\"w-6 h-6 bg-white bg-opacity-20 rounded-full flex items-center justify-center\">\n                  <svg class=\"w-3 h-3 text-white\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                    <path fill-rule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clip-rule=\"evenodd\" />\n                  </svg>\n                </div>\n                <span class=\"text-white text-opacity-90\">AI-powered customer insights</span>\n              </div>\n              <div class=\"flex items-center space-x-3\">\n                <div class=\"w-6 h-6 bg-white bg-opacity-20 rounded-full flex items-center justify-center\">\n                  <svg class=\"w-3 h-3 text-white\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                    <path fill-rule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clip-rule=\"evenodd\" />\n                  </svg>\n                </div>\n                <span class=\"text-white text-opacity-90\">Automated invoice generation</span>\n              </div>\n              <div class=\"flex items-center space-x-3\">\n                <div class=\"w-6 h-6 bg-white bg-opacity-20 rounded-full flex items-center justify-center\">\n                  <svg class=\"w-3 h-3 text-white\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                    <path fill-rule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clip-rule=\"evenodd\" />\n                  </svg>\n                </div>\n                <span class=\"text-white text-opacity-90\">Smart financial forecasting</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script setup>\nimport axios from 'axios'\nimport { ref, computed } from 'vue'\nimport { useNotificationStore } from '@/scripts/stores/notification'\nimport { useRouter } from 'vue-router'\nimport { required, email, helpers } from '@vuelidate/validators'\nimport { useVuelidate } from '@vuelidate/core'\nimport { useI18n } from 'vue-i18n'\nimport { useAuthStore } from '@/scripts/admin/stores/auth'\n\nconst notificationStore = useNotificationStore()\nconst authStore = useAuthStore()\nconst { t } = useI18n()\nconst router = useRouter()\nconst isLoading = ref(false)\nconst isShowPassword = ref(false)\n\nconst rules = {\n  email: {\n    required: helpers.withMessage(t('validation.required'), required),\n    email: helpers.withMessage(t('validation.email_incorrect'), email),\n  },\n  password: {\n    required: helpers.withMessage(t('validation.required'), required),\n  },\n}\n\nconst v$ = useVuelidate(\n  rules,\n  computed(() => authStore.loginData)\n)\n\nconst getInputType = computed(() => {\n  return isShowPassword.value ? 'text' : 'password'\n})\n\nasync function onSubmit() {\n  axios.defaults.withCredentials = true\n\n  v$.value.$touch()\n\n  if (v$.value.$invalid) {\n    return\n  }\n\n  isLoading.value = true\n\n  try {\n    await authStore.login(authStore.loginData)\n    \n    router.push('/admin/dashboard')\n    \n    notificationStore.showNotification({\n      type: 'success',\n      message: 'Welcome to Innovars AI System CRM! 🎉',\n    })\n  } catch (error) {\n    notificationStore.showNotification({\n      type: 'error',\n      message: 'Login failed. Please check your credentials.',\n    })\n  } finally {\n    isLoading.value = false\n  }\n}\n</script>\n\n<style scoped>\n.input-modern {\n  padding-left: 2.5rem;\n}\n</style>