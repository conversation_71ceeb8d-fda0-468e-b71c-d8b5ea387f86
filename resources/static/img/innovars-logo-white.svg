<svg width="200" height="60" viewBox="0 0 200 60" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="gradient1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#60A5FA;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#A78BFA;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- AI <PERSON>con -->
  <g transform="translate(10, 10)">
    <circle cx="20" cy="20" r="18" fill="rgba(255,255,255,0.1)"/>
    <path d="M20 4C11.163 4 4 11.163 4 20s7.163 16 16 16 16-7.163 16-16S28.837 4 20 4zm0 28c-6.627 0-12-5.373-12-12S13.373 8 20 8s12 5.373 12 12-5.373 12-12 12z" fill="white"/>
    <circle cx="15" cy="16" r="2" fill="white"/>
    <circle cx="25" cy="16" r="2" fill="white"/>
    <circle cx="20" cy="24" r="1.5" fill="white"/>
    <path d="M12 20h4m4 0h4m-8 4h4" stroke="white" stroke-width="1.5" stroke-linecap="round"/>
  </g>
  
  <!-- Text -->
  <text x="55" y="25" font-family="Poppins, sans-serif" font-size="18" font-weight="600" fill="white">Innovars</text>
  <text x="55" y="42" font-family="Poppins, sans-serif" font-size="12" font-weight="400" fill="rgba(255,255,255,0.8)">AI System CRM</text>
</svg>