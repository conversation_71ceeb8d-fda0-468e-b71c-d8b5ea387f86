<svg width="200" height="60" viewBox="0 0 200 60" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="gradient1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#0EA5E9;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#8B5CF6;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="gradient2" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#A855F7;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#0284C7;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- AI Brain Icon -->
  <g transform="translate(10, 10)">
    <circle cx="20" cy="20" r="18" fill="url(#gradient1)" opacity="0.1"/>
    <path d="M20 4C11.163 4 4 11.163 4 20s7.163 16 16 16 16-7.163 16-16S28.837 4 20 4zm0 28c-6.627 0-12-5.373-12-12S13.373 8 20 8s12 5.373 12 12-5.373 12-12 12z" fill="url(#gradient1)"/>
    <circle cx="15" cy="16" r="2" fill="url(#gradient1)"/>
    <circle cx="25" cy="16" r="2" fill="url(#gradient1)"/>
    <circle cx="20" cy="24" r="1.5" fill="url(#gradient1)"/>
    <path d="M12 20h4m4 0h4m-8 4h4" stroke="url(#gradient1)" stroke-width="1.5" stroke-linecap="round"/>
  </g>
  
  <!-- Text -->
  <text x="55" y="25" font-family="Poppins, sans-serif" font-size="18" font-weight="600" fill="url(#gradient1)">Innovars</text>
  <text x="55" y="42" font-family="Poppins, sans-serif" font-size="12" font-weight="400" fill="url(#gradient2)">AI System CRM</text>
</svg>