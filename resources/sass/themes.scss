// Innovars AI System CRM Theme
// Modern gradient-based color palette

:root {
  // Primary Brand Colors - Modern Blue/Purple Gradient
  --color-primary-50: 240, 249, 255;
  --color-primary-100: 224, 242, 254;
  --color-primary-200: 186, 230, 253;
  --color-primary-300: 125, 211, 252;
  --color-primary-400: 56, 189, 248;
  --color-primary-500: 14, 165, 233;
  --color-primary-600: 2, 132, 199;
  --color-primary-700: 3, 105, 161;
  --color-primary-800: 7, 89, 133;
  --color-primary-900: 12, 74, 110;

  // Secondary Colors - Purple Accent
  --color-secondary-50: 250, 245, 255;
  --color-secondary-100: 243, 232, 255;
  --color-secondary-200: 233, 213, 255;
  --color-secondary-300: 196, 181, 253;
  --color-secondary-400: 167, 139, 250;
  --color-secondary-500: 139, 92, 246;
  --color-secondary-600: 124, 58, 237;
  --color-secondary-700: 109, 40, 217;
  --color-secondary-800: 91, 33, 182;
  --color-secondary-900: 76, 29, 149;

  // Success Colors - Modern Green
  --color-success-50: 240, 253, 244;
  --color-success-100: 220, 252, 231;
  --color-success-200: 187, 247, 208;
  --color-success-300: 134, 239, 172;
  --color-success-400: 74, 222, 128;
  --color-success-500: 34, 197, 94;
  --color-success-600: 22, 163, 74;
  --color-success-700: 21, 128, 61;
  --color-success-800: 22, 101, 52;
  --color-success-900: 20, 83, 45;

  // Warning Colors - Modern Orange
  --color-warning-50: 255, 251, 235;
  --color-warning-100: 254, 243, 199;
  --color-warning-200: 253, 230, 138;
  --color-warning-300: 252, 211, 77;
  --color-warning-400: 251, 191, 36;
  --color-warning-500: 245, 158, 11;
  --color-warning-600: 217, 119, 6;
  --color-warning-700: 180, 83, 9;
  --color-warning-800: 146, 64, 14;
  --color-warning-900: 120, 53, 15;

  // Error Colors - Modern Red
  --color-error-50: 254, 242, 242;
  --color-error-100: 254, 226, 226;
  --color-error-200: 254, 202, 202;
  --color-error-300: 252, 165, 165;
  --color-error-400: 248, 113, 113;
  --color-error-500: 239, 68, 68;
  --color-error-600: 220, 38, 38;
  --color-error-700: 185, 28, 28;
  --color-error-800: 153, 27, 27;
  --color-error-900: 127, 29, 29;

  // Neutral Colors - Modern Gray Scale
  --color-gray-50: 248, 250, 252;
  --color-gray-100: 241, 245, 249;
  --color-gray-200: 226, 232, 240;
  --color-gray-300: 203, 213, 225;
  --color-gray-400: 148, 163, 184;
  --color-gray-500: 100, 116, 139;
  --color-gray-600: 71, 85, 105;
  --color-gray-700: 51, 65, 85;
  --color-gray-800: 30, 41, 59;
  --color-gray-900: 15, 23, 42;

  // Background Colors
  --bg-primary: 255, 255, 255;
  --bg-secondary: 248, 250, 252;
  --bg-tertiary: 241, 245, 249;
  
  // Text Colors
  --text-primary: 15, 23, 42;
  --text-secondary: 51, 65, 85;
  --text-muted: 100, 116, 139;

  // Border Colors
  --border-light: 226, 232, 240;
  --border-medium: 203, 213, 225;
  --border-dark: 148, 163, 184;

  // Shadow Colors
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

// Dark theme support
@media (prefers-color-scheme: dark) {
  :root {
    --bg-primary: 15, 23, 42;
    --bg-secondary: 30, 41, 59;
    --bg-tertiary: 51, 65, 85;
    
    --text-primary: 248, 250, 252;
    --text-secondary: 226, 232, 240;
    --text-muted: 148, 163, 184;
    
    --border-light: 71, 85, 105;
    --border-medium: 100, 116, 139;
    --border-dark: 148, 163, 184;
  }
}

// Custom Innovars AI gradients
.gradient-primary {
  background: linear-gradient(135deg, rgb(var(--color-primary-500)) 0%, rgb(var(--color-secondary-500)) 100%);
}

.gradient-secondary {
  background: linear-gradient(135deg, rgb(var(--color-secondary-400)) 0%, rgb(var(--color-primary-600)) 100%);
}

.gradient-success {
  background: linear-gradient(135deg, rgb(var(--color-success-400)) 0%, rgb(var(--color-success-600)) 100%);
}

// Glass morphism effects
.glass {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.glass-dark {
  background: rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

// Modern card styles
.card-modern {
  background: rgb(var(--bg-primary));
  border-radius: 16px;
  box-shadow: var(--shadow-lg);
  border: 1px solid rgb(var(--border-light));
  transition: all 0.3s ease;
}

.card-modern:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl);
}

// Button styles
.btn-primary {
  @apply gradient-primary text-white font-semibold py-3 px-6 rounded-xl shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 transition-all duration-200;
}

.btn-secondary {
  @apply bg-white text-gray-700 font-semibold py-3 px-6 rounded-xl border border-gray-200 shadow-md hover:shadow-lg transform hover:-translate-y-0.5 transition-all duration-200;
}

// Input styles
.input-modern {
  @apply w-full px-4 py-3 rounded-xl border border-gray-200 focus:border-primary-500 focus:ring-2 focus:ring-primary-200 transition-all duration-200 bg-white;
}

// Sidebar styles
.sidebar-modern {
  background: linear-gradient(180deg, rgb(var(--color-primary-600)) 0%, rgb(var(--color-primary-800)) 100%);
  backdrop-filter: blur(20px);
}

// Header styles
.header-modern {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgb(var(--border-light));
}