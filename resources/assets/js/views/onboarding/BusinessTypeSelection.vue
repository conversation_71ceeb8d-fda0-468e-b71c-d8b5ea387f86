<template>
  <div class="business-type-selection">
    <div class="max-w-6xl mx-auto px-4 py-8">
      <div class="text-center mb-12">
        <h1 class="text-4xl font-bold text-gray-900 mb-4">
          Kies uw bedrijfstype
        </h1>
        <p class="text-xl text-gray-600 max-w-3xl mx-auto">
          Selecteer het type bedrijf dat het beste bij u past. We configureren uw AI-agenten en dashboard specifiek voor uw branche.
        </p>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        <div
          v-for="businessType in businessTypes"
          :key="businessType.id"
          @click="selectBusinessType(businessType)"
          class="business-type-card cursor-pointer transform transition-all duration-200 hover:scale-105"
          :class="{ 'selected': selectedBusinessType?.id === businessType.id }"
        >
          <div class="bg-white rounded-xl shadow-lg p-8 border-2 border-transparent hover:border-blue-500">
            <div class="text-center">
              <div class="w-16 h-16 mx-auto mb-4 bg-blue-100 rounded-full flex items-center justify-center">
                <i :class="`fas fa-${businessType.icon} text-2xl text-blue-600`"></i>
              </div>
              
              <h3 class="text-xl font-semibold text-gray-900 mb-3">
                {{ businessType.name }}
              </h3>
              
              <p class="text-gray-600 mb-6">
                {{ businessType.description }}
              </p>

              <div class="space-y-2">
                <h4 class="font-medium text-gray-900">Inbegrepen functies:</h4>
                <ul class="text-sm text-gray-600 space-y-1">
                  <li v-for="feature in businessType.features.slice(0, 4)" :key="feature">
                    <i class="fas fa-check text-green-500 mr-2"></i>
                    {{ formatFeature(feature) }}
                  </li>
                  <li v-if="businessType.features.length > 4" class="text-blue-600">
                    +{{ businessType.features.length - 4 }} meer...
                  </li>
                </ul>
              </div>

              <div v-if="businessType.whatsapp_enabled" class="mt-4 flex items-center justify-center text-green-600">
                <i class="fab fa-whatsapp mr-2"></i>
                <span class="text-sm font-medium">WhatsApp integratie</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div v-if="selectedBusinessType" class="mt-12 text-center">
        <div class="bg-blue-50 rounded-lg p-6 mb-8">
          <h3 class="text-lg font-semibold text-blue-900 mb-2">
            {{ selectedBusinessType.name }} geselecteerd
          </h3>
          <p class="text-blue-700">
            We gaan uw account configureren met de juiste AI-agenten en dashboard voor {{ selectedBusinessType.name.toLowerCase() }}.
          </p>
        </div>

        <button
          @click="continueSetup"
          :disabled="loading"
          class="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-8 rounded-lg transition-colors duration-200 disabled:opacity-50"
        >
          <i v-if="loading" class="fas fa-spinner fa-spin mr-2"></i>
          {{ loading ? 'Account wordt aangemaakt...' : 'Doorgaan met setup' }}
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import axios from 'axios'

export default {
  name: 'BusinessTypeSelection',
  
  data() {
    return {
      businessTypes: [],
      selectedBusinessType: null,
      loading: false,
    }
  },

  async mounted() {
    await this.fetchBusinessTypes()
  },

  methods: {
    async fetchBusinessTypes() {
      try {
        const response = await axios.get('/api/v1/tenants/business-types')
        this.businessTypes = response.data
      } catch (error) {
        console.error('Error fetching business types:', error)
        this.$toast.error('Fout bij het laden van bedrijfstypes')
      }
    },

    selectBusinessType(businessType) {
      this.selectedBusinessType = businessType
    },

    async continueSetup() {
      if (!this.selectedBusinessType) return

      this.loading = true

      try {
        // Here you would typically create the tenant account
        // For now, we'll just simulate the process
        await new Promise(resolve => setTimeout(resolve, 2000))

        // Redirect to dashboard or next step
        this.$router.push('/dashboard')
        
        this.$toast.success(`Account succesvol aangemaakt voor ${this.selectedBusinessType.name}!`)
      } catch (error) {
        console.error('Error creating tenant:', error)
        this.$toast.error('Fout bij het aanmaken van account')
      } finally {
        this.loading = false
      }
    },

    formatFeature(feature) {
      // Convert snake_case to readable text
      return feature
        .replace(/_/g, ' ')
        .replace(/\b\w/g, l => l.toUpperCase())
    }
  }
}
</script>

<style scoped>
.business-type-card.selected .bg-white {
  @apply border-blue-500 bg-blue-50;
}

.business-type-card:hover {
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.business-type-card.selected {
  transform: scale(1.02);
}
</style>
