<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Crater\Models\AIAgent;
use Crater\Models\AITask;
use Crater\Models\SystemLog;

class AIDashboardSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create AI Agents
        $agents = [
            [
                'name' => 'Claude 3.5 Sonnet',
                'type' => 'openrouter',
                'model' => 'anthropic/claude-3.5-sonnet',
                'status' => 'running',
                'description' => 'Advanced reasoning and code generation',
                'configuration' => [
                    'api_key' => 'sk-or-v1-***',
                    'max_tokens' => 4096,
                    'temperature' => 0.7
                ],
                'last_activity' => now(),
                'total_requests' => 156,
                'successful_requests' => 152,
                'failed_requests' => 4,
                'average_response_time' => 2.3,
                'auto_start' => true
            ],
            [
                'name' => 'GPT-4o',
                'type' => 'openrouter',
                'model' => 'openai/gpt-4o',
                'status' => 'running',
                'description' => 'Multimodal AI for text and vision tasks',
                'configuration' => [
                    'api_key' => 'sk-or-v1-***',
                    'max_tokens' => 4096,
                    'temperature' => 0.5
                ],
                'last_activity' => now()->subMinutes(5),
                'total_requests' => 89,
                'successful_requests' => 87,
                'failed_requests' => 2,
                'average_response_time' => 1.8,
                'auto_start' => true
            ],
            [
                'name' => 'CodeLlama 34B',
                'type' => 'ollama',
                'model' => 'codellama:34b',
                'status' => 'running',
                'description' => 'Local code generation and analysis',
                'configuration' => [
                    'base_url' => 'http://localhost:11434',
                    'context_length' => 16384
                ],
                'last_activity' => now()->subMinutes(2),
                'total_requests' => 45,
                'successful_requests' => 44,
                'failed_requests' => 1,
                'average_response_time' => 3.2,
                'auto_start' => true
            ],
            [
                'name' => 'DeepSeek Coder',
                'type' => 'ollama',
                'model' => 'deepseek-coder:33b',
                'status' => 'stopped',
                'description' => 'Specialized coding assistant',
                'configuration' => [
                    'base_url' => 'http://localhost:11434',
                    'context_length' => 16384
                ],
                'last_activity' => now()->subHours(2),
                'total_requests' => 23,
                'successful_requests' => 23,
                'failed_requests' => 0,
                'average_response_time' => 4.1,
                'auto_start' => false
            ],
            [
                'name' => 'LM Studio Local',
                'type' => 'lmstudio',
                'model' => 'llama-3.1-8b-instruct',
                'status' => 'error',
                'description' => 'Local LM Studio instance',
                'configuration' => [
                    'base_url' => 'http://localhost:1234',
                    'context_length' => 8192
                ],
                'last_activity' => now()->subHours(6),
                'total_requests' => 12,
                'successful_requests' => 8,
                'failed_requests' => 4,
                'average_response_time' => 5.7,
                'auto_start' => false
            ]
        ];

        foreach ($agents as $agentData) {
            AIAgent::create($agentData);
        }

        // Create sample tasks
        $tasks = [
            [
                'name' => 'Daily Email Summary',
                'type' => 'email',
                'status' => 'completed',
                'description' => 'Generate and send daily summary email to stakeholders',
                'parameters' => [
                    'recipients' => ['<EMAIL>'],
                    'template' => 'daily_summary',
                    'data_source' => 'crm_analytics'
                ],
                'result' => [
                    'sent_at' => now()->subHours(2)->toISOString(),
                    'recipients_count' => 1,
                    'status' => 'delivered'
                ],
                'agent_type' => 'openrouter',
                'started_at' => now()->subHours(2)->subMinutes(5),
                'completed_at' => now()->subHours(2),
                'created_by' => 1
            ],
            [
                'name' => 'Code Review Analysis',
                'type' => 'analysis',
                'status' => 'running',
                'description' => 'Analyze recent code commits for quality and security',
                'parameters' => [
                    'repository' => 'crater-crm',
                    'branch' => 'main',
                    'commits_limit' => 10
                ],
                'agent_type' => 'ollama',
                'started_at' => now()->subMinutes(15),
                'created_by' => 1
            ],
            [
                'name' => 'Customer Sentiment Analysis',
                'type' => 'analysis',
                'status' => 'pending',
                'description' => 'Analyze customer feedback and support tickets',
                'parameters' => [
                    'date_range' => '7_days',
                    'sources' => ['support_tickets', 'reviews', 'surveys']
                ],
                'agent_type' => 'openrouter',
                'scheduled_at' => now()->addHours(1),
                'created_by' => 1
            ],
            [
                'name' => 'Automated Invoice Processing',
                'type' => 'automation',
                'status' => 'failed',
                'description' => 'Process pending invoices and send reminders',
                'parameters' => [
                    'overdue_days' => 30,
                    'reminder_template' => 'payment_reminder'
                ],
                'error_message' => 'SMTP configuration error: Connection timeout',
                'agent_type' => 'openrouter',
                'started_at' => now()->subHours(1),
                'completed_at' => now()->subHours(1)->addMinutes(2),
                'retry_count' => 1,
                'created_by' => 1
            ]
        ];

        foreach ($tasks as $taskData) {
            AITask::create($taskData);
        }

        // Create system logs
        $logs = [
            [
                'level' => 'info',
                'category' => 'ai_agent',
                'event' => 'agent_started',
                'message' => 'AI Agent Claude 3.5 Sonnet started successfully',
                'context' => ['agent_id' => 1],
                'created_at' => now()->subMinutes(30)
            ],
            [
                'level' => 'info',
                'category' => 'task_scheduler',
                'event' => 'task_completed',
                'message' => 'Daily Email Summary task completed successfully',
                'context' => ['task_id' => 1, 'duration' => '5 minutes'],
                'created_at' => now()->subHours(2)
            ],
            [
                'level' => 'warning',
                'category' => 'ai_agent',
                'event' => 'high_response_time',
                'message' => 'DeepSeek Coder response time above threshold: 4.1s',
                'context' => ['agent_id' => 4, 'response_time' => 4.1],
                'created_at' => now()->subMinutes(45)
            ],
            [
                'level' => 'error',
                'category' => 'email',
                'event' => 'smtp_connection_failed',
                'message' => 'SMTP connection timeout while processing invoice reminders',
                'context' => ['task_id' => 4, 'error' => 'Connection timeout'],
                'created_at' => now()->subHours(1)
            ],
            [
                'level' => 'info',
                'category' => 'api',
                'event' => 'openrouter_request',
                'message' => 'OpenRouter API request successful',
                'context' => ['model' => 'claude-3.5-sonnet', 'tokens' => 1024],
                'created_at' => now()->subMinutes(10)
            ],
            [
                'level' => 'debug',
                'category' => 'task_scheduler',
                'event' => 'task_queued',
                'message' => 'Customer Sentiment Analysis task queued for execution',
                'context' => ['task_id' => 3, 'scheduled_at' => now()->addHours(1)->toISOString()],
                'created_at' => now()->subMinutes(5)
            ]
        ];

        foreach ($logs as $logData) {
            SystemLog::create($logData);
        }
    }
}
