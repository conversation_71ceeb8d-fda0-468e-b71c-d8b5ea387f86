<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Crater\Models\BusinessType;
use Crater\Models\AgentTemplate;

class AgentTemplateSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Get all business types
        $businessTypes = BusinessType::all();

        foreach ($businessTypes as $businessType) {
            $this->createAgentTemplatesForBusinessType($businessType);
        }
    }

    private function createAgentTemplatesForBusinessType(BusinessType $businessType)
    {
        $commonTemplates = $this->getCommonAgentTemplates($businessType->id);
        $specificTemplates = $this->getBusinessSpecificTemplates($businessType);

        $allTemplates = array_merge($commonTemplates, $specificTemplates);

        foreach ($allTemplates as $template) {
            AgentTemplate::create($template);
        }
    }

    private function getCommonAgentTemplates($businessTypeId)
    {
        return [
            [
                'business_type_id' => $businessTypeId,
                'name' => 'Customer Service Agent',
                'agent_type' => 'customer_service',
                'description' => 'Handles general customer inquiries and support',
                'icon' => 'support',
                'default_config' => [
                    'response_time' => 'immediate',
                    'escalation_threshold' => 3,
                    'working_hours' => '24/7'
                ],
                'capabilities' => [
                    'answer_questions',
                    'handle_complaints',
                    'provide_information',
                    'escalate_issues',
                    'collect_feedback'
                ],
                'triggers' => [
                    'keywords' => ['help', 'support', 'problem', 'issue', 'question'],
                    'sentiment' => ['negative', 'confused'],
                    'message_type' => ['text', 'voice']
                ],
                'responses' => [
                    'greeting' => 'Hallo! Ik ben uw AI assistent. Hoe kan ik u vandaag helpen?',
                    'escalation' => 'Ik ga dit doorsturen naar een van onze medewerkers. U hoort binnen 15 minuten van ons.',
                    'closing' => 'Is er nog iets anders waarmee ik u kan helpen?'
                ],
                'is_required' => true,
                'is_active' => true,
                'sort_order' => 1,
            ],
            [
                'business_type_id' => $businessTypeId,
                'name' => 'Communication Agent',
                'agent_type' => 'communication',
                'description' => 'Manages automated communications and notifications',
                'icon' => 'message',
                'default_config' => [
                    'auto_reply' => true,
                    'notification_delay' => 5,
                    'template_usage' => true
                ],
                'capabilities' => [
                    'send_notifications',
                    'auto_reply',
                    'schedule_messages',
                    'template_management',
                    'broadcast_messages'
                ],
                'triggers' => [
                    'events' => ['booking_confirmed', 'status_update', 'reminder_due'],
                    'time_based' => ['daily_summary', 'weekly_report'],
                    'user_action' => ['new_registration', 'payment_received']
                ],
                'responses' => [
                    'confirmation' => 'Uw aanvraag is bevestigd. Referentienummer: {reference}',
                    'reminder' => 'Herinnering: U heeft een afspraak op {date} om {time}',
                    'update' => 'Status update: {status_message}'
                ],
                'is_required' => false,
                'is_active' => true,
                'sort_order' => 2,
            ],
            [
                'business_type_id' => $businessTypeId,
                'name' => 'Analytics Agent',
                'agent_type' => 'analytics',
                'description' => 'Provides insights and reports on business performance',
                'icon' => 'analytics',
                'default_config' => [
                    'report_frequency' => 'daily',
                    'metrics_tracking' => true,
                    'alert_thresholds' => []
                ],
                'capabilities' => [
                    'generate_reports',
                    'track_metrics',
                    'send_alerts',
                    'data_analysis',
                    'trend_identification'
                ],
                'triggers' => [
                    'schedule' => ['daily_report', 'weekly_summary', 'monthly_analysis'],
                    'thresholds' => ['low_performance', 'high_volume', 'anomaly_detected'],
                    'requests' => ['manual_report', 'custom_analysis']
                ],
                'responses' => [
                    'daily_report' => 'Dagelijks rapport: {metrics_summary}',
                    'alert' => 'Alert: {metric_name} heeft de drempelwaarde overschreden',
                    'analysis' => 'Analyse resultaat: {insights}'
                ],
                'is_required' => false,
                'is_active' => true,
                'sort_order' => 3,
            ]
        ];
    }

    private function getBusinessSpecificTemplates(BusinessType $businessType)
    {
        switch ($businessType->slug) {
            case 'taxi-transport':
                return $this->getTaxiAgentTemplates($businessType->id);
            case 'construction':
                return $this->getConstructionAgentTemplates($businessType->id);
            case 'car-rental':
                return $this->getCarRentalAgentTemplates($businessType->id);
            case 'restaurant':
                return $this->getRestaurantAgentTemplates($businessType->id);
            case 'ecommerce':
                return $this->getEcommerceAgentTemplates($businessType->id);
            case 'healthcare':
                return $this->getHealthcareAgentTemplates($businessType->id);
            case 'real-estate':
                return $this->getRealEstateAgentTemplates($businessType->id);
            default:
                return [];
        }
    }

    private function getTaxiAgentTemplates($businessTypeId)
    {
        return [
            [
                'business_type_id' => $businessTypeId,
                'name' => 'Booking Agent',
                'agent_type' => 'booking',
                'description' => 'Handles taxi booking requests and confirmations',
                'icon' => 'calendar',
                'default_config' => [
                    'auto_assign_driver' => true,
                    'eta_calculation' => true,
                    'fare_estimation' => true
                ],
                'capabilities' => [
                    'process_bookings',
                    'assign_drivers',
                    'calculate_eta',
                    'estimate_fare',
                    'send_confirmations',
                    'track_rides'
                ],
                'triggers' => [
                    'keywords' => ['boek', 'taxi', 'rit', 'ophalen', 'naar'],
                    'location_shared' => true,
                    'booking_request' => true
                ],
                'responses' => [
                    'booking_request' => 'Ik help u graag met het boeken van een taxi. Wat is uw ophaaladres?',
                    'confirmation' => 'Uw taxi is geboekt! Ritnummer: {booking_number}. ETA: {eta} minuten.',
                    'driver_assigned' => 'Chauffeur {driver_name} is onderweg. Kenteken: {license_plate}'
                ],
                'is_required' => true,
                'is_active' => true,
                'sort_order' => 10,
            ],
            [
                'business_type_id' => $businessTypeId,
                'name' => 'Route Optimizer',
                'agent_type' => 'route_optimizer',
                'description' => 'Optimizes routes and manages traffic information',
                'icon' => 'route',
                'default_config' => [
                    'traffic_integration' => true,
                    'route_alternatives' => 3,
                    'real_time_updates' => true
                ],
                'capabilities' => [
                    'optimize_routes',
                    'traffic_analysis',
                    'alternative_routes',
                    'time_estimation',
                    'fuel_optimization'
                ],
                'triggers' => [
                    'route_request' => true,
                    'traffic_update' => true,
                    'driver_location' => true
                ],
                'responses' => [
                    'route_optimized' => 'Beste route berekend. Geschatte tijd: {duration} minuten.',
                    'traffic_alert' => 'Verkeersprobleem gedetecteerd. Alternatieve route voorgesteld.',
                    'arrival_update' => 'Nieuwe aankomsttijd: {eta} door verkeerssituatie.'
                ],
                'is_required' => false,
                'is_active' => true,
                'sort_order' => 11,
            ]
        ];
    }

    private function getConstructionAgentTemplates($businessTypeId)
    {
        return [
            [
                'business_type_id' => $businessTypeId,
                'name' => 'Project Manager Agent',
                'agent_type' => 'project_manager',
                'description' => 'Manages construction projects and timelines',
                'icon' => 'project',
                'default_config' => [
                    'milestone_tracking' => true,
                    'budget_monitoring' => true,
                    'progress_reporting' => 'weekly'
                ],
                'capabilities' => [
                    'track_progress',
                    'manage_milestones',
                    'budget_monitoring',
                    'schedule_updates',
                    'client_reporting'
                ],
                'triggers' => [
                    'milestone_reached' => true,
                    'budget_threshold' => true,
                    'schedule_delay' => true
                ],
                'responses' => [
                    'milestone_update' => 'Mijlpaal bereikt: {milestone_name}. Project is {percentage}% voltooid.',
                    'budget_alert' => 'Budget waarschuwing: {percentage}% van budget gebruikt.',
                    'progress_report' => 'Weekrapport: {progress_summary}'
                ],
                'is_required' => true,
                'is_active' => true,
                'sort_order' => 10,
            ],
            [
                'business_type_id' => $businessTypeId,
                'name' => 'Material Tracker',
                'agent_type' => 'material_tracker',
                'description' => 'Tracks material orders and deliveries',
                'icon' => 'inventory',
                'default_config' => [
                    'auto_reorder' => false,
                    'delivery_tracking' => true,
                    'quality_checks' => true
                ],
                'capabilities' => [
                    'track_materials',
                    'monitor_deliveries',
                    'quality_control',
                    'inventory_management',
                    'supplier_communication'
                ],
                'triggers' => [
                    'low_stock' => true,
                    'delivery_scheduled' => true,
                    'quality_issue' => true
                ],
                'responses' => [
                    'delivery_notification' => 'Materiaal geleverd: {material_name}. Kwaliteitscontrole gepland.',
                    'low_stock_alert' => 'Voorraad laag: {material_name}. Nieuwe bestelling nodig.',
                    'quality_issue' => 'Kwaliteitsprobleem gedetecteerd bij {material_name}. Leverancier gecontacteerd.'
                ],
                'is_required' => false,
                'is_active' => true,
                'sort_order' => 11,
            ]
        ];
    }

    private function getCarRentalAgentTemplates($businessTypeId)
    {
        return [
            [
                'business_type_id' => $businessTypeId,
                'name' => 'Fleet Manager',
                'agent_type' => 'fleet_manager',
                'description' => 'Manages vehicle fleet and availability',
                'icon' => 'fleet',
                'default_config' => [
                    'availability_tracking' => true,
                    'maintenance_scheduling' => true,
                    'utilization_optimization' => true
                ],
                'capabilities' => [
                    'track_availability',
                    'schedule_maintenance',
                    'optimize_utilization',
                    'manage_reservations',
                    'vehicle_assignment'
                ],
                'triggers' => [
                    'booking_request' => true,
                    'maintenance_due' => true,
                    'vehicle_return' => true
                ],
                'responses' => [
                    'availability_check' => 'Beschikbare voertuigen: {vehicle_list}',
                    'maintenance_reminder' => 'Onderhoud gepland voor {vehicle} op {date}',
                    'booking_confirmation' => 'Voertuig gereserveerd: {vehicle_details}'
                ],
                'is_required' => true,
                'is_active' => true,
                'sort_order' => 10,
            ]
        ];
    }

    private function getRestaurantAgentTemplates($businessTypeId)
    {
        return [
            [
                'business_type_id' => $businessTypeId,
                'name' => 'Order Manager',
                'agent_type' => 'order_manager',
                'description' => 'Manages food orders and delivery coordination',
                'icon' => 'order',
                'default_config' => [
                    'order_confirmation' => true,
                    'delivery_tracking' => true,
                    'preparation_time' => 30
                ],
                'capabilities' => [
                    'process_orders',
                    'track_preparation',
                    'coordinate_delivery',
                    'manage_queue',
                    'customer_updates'
                ],
                'triggers' => [
                    'new_order' => true,
                    'order_ready' => true,
                    'delivery_update' => true
                ],
                'responses' => [
                    'order_confirmation' => 'Bestelling ontvangen! Ordernummer: {order_number}. Bereidingstijd: {prep_time} minuten.',
                    'order_ready' => 'Uw bestelling is klaar voor ophalen/bezorging.',
                    'delivery_update' => 'Uw bestelling is onderweg. Verwachte aankomst: {eta}'
                ],
                'is_required' => true,
                'is_active' => true,
                'sort_order' => 10,
            ]
        ];
    }

    private function getEcommerceAgentTemplates($businessTypeId)
    {
        return [
            [
                'business_type_id' => $businessTypeId,
                'name' => 'Sales Agent',
                'agent_type' => 'sales',
                'description' => 'Handles product inquiries and sales support',
                'icon' => 'sales',
                'default_config' => [
                    'product_recommendations' => true,
                    'inventory_check' => true,
                    'upselling' => true
                ],
                'capabilities' => [
                    'product_information',
                    'inventory_check',
                    'price_quotes',
                    'recommendations',
                    'order_assistance'
                ],
                'triggers' => [
                    'product_inquiry' => true,
                    'cart_abandonment' => true,
                    'price_request' => true
                ],
                'responses' => [
                    'product_info' => 'Productinformatie: {product_details}',
                    'stock_status' => 'Voorraadstatus: {availability}',
                    'recommendation' => 'Gebaseerd op uw interesse, raden wij ook aan: {recommendations}'
                ],
                'is_required' => true,
                'is_active' => true,
                'sort_order' => 10,
            ]
        ];
    }

    private function getHealthcareAgentTemplates($businessTypeId)
    {
        return [
            [
                'business_type_id' => $businessTypeId,
                'name' => 'Appointment Scheduler',
                'agent_type' => 'appointment_scheduler',
                'description' => 'Manages medical appointments and scheduling',
                'icon' => 'calendar-medical',
                'default_config' => [
                    'appointment_types' => ['consultation', 'checkup', 'follow_up'],
                    'booking_window' => 30,
                    'reminder_schedule' => [24, 2]
                ],
                'capabilities' => [
                    'schedule_appointments',
                    'send_reminders',
                    'manage_cancellations',
                    'check_availability',
                    'patient_communication'
                ],
                'triggers' => [
                    'appointment_request' => true,
                    'reminder_due' => true,
                    'cancellation_request' => true
                ],
                'responses' => [
                    'appointment_booked' => 'Afspraak geboekt voor {date} om {time} met {doctor}',
                    'reminder' => 'Herinnering: Afspraak morgen om {time}',
                    'cancellation' => 'Afspraak geannuleerd. Wilt u een nieuwe afspraak inplannen?'
                ],
                'is_required' => true,
                'is_active' => true,
                'sort_order' => 10,
            ]
        ];
    }

    private function getRealEstateAgentTemplates($businessTypeId)
    {
        return [
            [
                'business_type_id' => $businessTypeId,
                'name' => 'Property Matcher',
                'agent_type' => 'property_matcher',
                'description' => 'Matches clients with suitable properties',
                'icon' => 'home-search',
                'default_config' => [
                    'search_criteria' => ['price', 'location', 'size', 'type'],
                    'match_threshold' => 80,
                    'viewing_scheduling' => true
                ],
                'capabilities' => [
                    'property_search',
                    'client_matching',
                    'viewing_coordination',
                    'market_analysis',
                    'property_recommendations'
                ],
                'triggers' => [
                    'property_inquiry' => true,
                    'new_listing' => true,
                    'viewing_request' => true
                ],
                'responses' => [
                    'property_match' => 'Ik heb {count} woningen gevonden die aan uw criteria voldoen',
                    'viewing_scheduled' => 'Bezichtiging gepland voor {date} om {time}',
                    'new_listing' => 'Nieuwe woning beschikbaar die aan uw wensen voldoet: {property_details}'
                ],
                'is_required' => true,
                'is_active' => true,
                'sort_order' => 10,
            ]
        ];
    }
}
