<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateWhatsappIntegrationsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('whatsapp_integrations', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('tenant_id');
            $table->string('business_phone_number');
            $table->string('phone_number_id');
            $table->string('whatsapp_business_account_id');
            $table->string('access_token');
            $table->string('webhook_verify_token');
            $table->string('webhook_url')->nullable();
            $table->enum('status', ['pending', 'verified', 'active', 'suspended'])->default('pending');
            $table->json('settings')->nullable(); // Auto-reply settings, business hours, etc.
            $table->json('message_templates')->nullable(); // Approved message templates
            $table->timestamp('verified_at')->nullable();
            $table->timestamps();

            $table->foreign('tenant_id')->references('id')->on('tenants')->onDelete('cascade');
            $table->unique(['tenant_id', 'business_phone_number']);
            $table->index(['status', 'tenant_id']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('whatsapp_integrations');
    }
}
