<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateConstructionMaterialsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('construction_materials', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('tenant_id');
            $table->unsignedBigInteger('project_id');
            $table->string('material_name');
            $table->string('material_type'); // cement, steel, wood, etc.
            $table->string('supplier_name')->nullable();
            $table->string('supplier_contact')->nullable();
            $table->decimal('quantity_ordered', 10, 2);
            $table->decimal('quantity_delivered', 10, 2)->default(0);
            $table->decimal('quantity_used', 10, 2)->default(0);
            $table->string('unit'); // kg, m3, pieces, etc.
            $table->decimal('unit_price', 8, 2);
            $table->decimal('total_cost', 10, 2);
            $table->enum('status', ['ordered', 'partial_delivery', 'delivered', 'in_use', 'completed'])->default('ordered');
            $table->date('order_date');
            $table->date('expected_delivery_date')->nullable();
            $table->date('actual_delivery_date')->nullable();
            $table->text('delivery_notes')->nullable();
            $table->string('invoice_number')->nullable();
            $table->json('quality_checks')->nullable();
            $table->timestamps();

            $table->foreign('tenant_id')->references('id')->on('tenants')->onDelete('cascade');
            $table->foreign('project_id')->references('id')->on('construction_projects')->onDelete('cascade');
            $table->index(['tenant_id', 'project_id']);
            $table->index(['status', 'expected_delivery_date']);
            $table->index(['material_type', 'status']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('construction_materials');
    }
}
