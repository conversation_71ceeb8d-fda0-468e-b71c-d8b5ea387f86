<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateRentalVehiclesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('rental_vehicles', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('tenant_id');
            $table->string('vehicle_number')->unique();
            $table->string('make');
            $table->string('model');
            $table->string('year');
            $table->string('color');
            $table->string('license_plate')->unique();
            $table->string('vin')->unique();
            $table->enum('vehicle_type', ['economy', 'compact', 'midsize', 'fullsize', 'luxury', 'suv', 'van', 'truck']);
            $table->enum('fuel_type', ['gasoline', 'diesel', 'hybrid', 'electric']);
            $table->enum('transmission', ['manual', 'automatic']);
            $table->integer('seats');
            $table->integer('doors');
            $table->decimal('daily_rate', 8, 2);
            $table->decimal('weekly_rate', 8, 2)->nullable();
            $table->decimal('monthly_rate', 8, 2)->nullable();
            $table->integer('mileage')->default(0);
            $table->enum('status', ['available', 'rented', 'maintenance', 'out_of_service'])->default('available');
            $table->date('registration_expiry')->nullable();
            $table->date('insurance_expiry')->nullable();
            $table->date('last_service_date')->nullable();
            $table->date('next_service_date')->nullable();
            $table->json('features')->nullable(); // AC, GPS, etc.
            $table->json('images')->nullable(); // Vehicle images
            $table->text('notes')->nullable();
            $table->timestamps();

            $table->foreign('tenant_id')->references('id')->on('tenants')->onDelete('cascade');
            $table->index(['tenant_id', 'status']);
            $table->index(['vehicle_type', 'status']);
            $table->index(['daily_rate', 'status']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('rental_vehicles');
    }
}
