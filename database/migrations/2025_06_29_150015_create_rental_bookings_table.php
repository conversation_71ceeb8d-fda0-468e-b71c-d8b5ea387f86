<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateRentalBookingsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('rental_bookings', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('tenant_id');
            $table->unsignedBigInteger('customer_id')->nullable();
            $table->unsignedBigInteger('vehicle_id');
            $table->string('booking_number')->unique();
            $table->string('customer_name');
            $table->string('customer_phone');
            $table->string('customer_email')->nullable();
            $table->string('customer_license_number');
            $table->date('license_expiry');
            $table->datetime('pickup_datetime');
            $table->datetime('return_datetime');
            $table->datetime('actual_pickup_datetime')->nullable();
            $table->datetime('actual_return_datetime')->nullable();
            $table->text('pickup_location');
            $table->text('return_location');
            $table->enum('status', ['pending', 'confirmed', 'picked_up', 'returned', 'cancelled', 'overdue'])->default('pending');
            $table->decimal('daily_rate', 8, 2);
            $table->integer('rental_days');
            $table->decimal('base_amount', 10, 2);
            $table->decimal('insurance_amount', 8, 2)->default(0);
            $table->decimal('additional_fees', 8, 2)->default(0);
            $table->decimal('discount_amount', 8, 2)->default(0);
            $table->decimal('total_amount', 10, 2);
            $table->decimal('deposit_amount', 8, 2)->default(0);
            $table->enum('payment_status', ['pending', 'partial', 'paid', 'refunded'])->default('pending');
            $table->string('payment_method')->nullable();
            $table->integer('pickup_mileage')->nullable();
            $table->integer('return_mileage')->nullable();
            $table->enum('fuel_level_pickup', ['empty', 'quarter', 'half', 'three_quarter', 'full'])->nullable();
            $table->enum('fuel_level_return', ['empty', 'quarter', 'half', 'three_quarter', 'full'])->nullable();
            $table->text('pickup_condition_notes')->nullable();
            $table->text('return_condition_notes')->nullable();
            $table->json('additional_drivers')->nullable();
            $table->text('special_requests')->nullable();
            $table->timestamps();

            $table->foreign('tenant_id')->references('id')->on('tenants')->onDelete('cascade');
            $table->foreign('customer_id')->references('id')->on('customers')->onDelete('set null');
            $table->foreign('vehicle_id')->references('id')->on('rental_vehicles')->onDelete('cascade');
            $table->index(['tenant_id', 'status']);
            $table->index(['pickup_datetime', 'return_datetime']);
            $table->index(['customer_phone', 'tenant_id']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('rental_bookings');
    }
}
