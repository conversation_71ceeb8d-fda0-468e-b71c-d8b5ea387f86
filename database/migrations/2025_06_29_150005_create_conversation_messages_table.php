<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateConversationMessagesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('conversation_messages', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('conversation_id');
            $table->string('whatsapp_message_id')->nullable();
            $table->enum('direction', ['inbound', 'outbound']);
            $table->enum('sender_type', ['customer', 'agent', 'human'])->default('agent');
            $table->text('message_text')->nullable();
            $table->enum('message_type', ['text', 'image', 'document', 'audio', 'video', 'location', 'template'])->default('text');
            $table->json('media_data')->nullable(); // For media messages
            $table->json('template_data')->nullable(); // For template messages
            $table->enum('status', ['sent', 'delivered', 'read', 'failed'])->default('sent');
            $table->json('metadata')->nullable();
            $table->timestamp('whatsapp_timestamp')->nullable();
            $table->timestamps();

            $table->foreign('conversation_id')->references('id')->on('agent_conversations')->onDelete('cascade');
            $table->index(['conversation_id', 'created_at']);
            $table->index(['whatsapp_message_id']);
            $table->index(['direction', 'sender_type']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('conversation_messages');
    }
}
