<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateAutomationRulesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('automation_rules', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('tenant_id');
            $table->unsignedBigInteger('ai_agent_id')->nullable();
            $table->string('name');
            $table->text('description')->nullable();
            $table->enum('trigger_type', ['message_received', 'keyword_detected', 'time_based', 'event_based', 'manual']);
            $table->json('conditions')->nullable(); // Conditions that must be met
            $table->json('actions')->nullable(); // Actions to perform
            $table->boolean('is_active')->default(true);
            $table->integer('priority')->default(0); // Higher priority rules execute first
            $table->integer('execution_count')->default(0);
            $table->timestamp('last_executed_at')->nullable();
            $table->timestamps();

            $table->foreign('tenant_id')->references('id')->on('tenants')->onDelete('cascade');
            $table->foreign('ai_agent_id')->references('id')->on('ai_agents')->onDelete('cascade');
            $table->index(['tenant_id', 'is_active', 'priority']);
            $table->index(['trigger_type', 'is_active']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('automation_rules');
    }
}
