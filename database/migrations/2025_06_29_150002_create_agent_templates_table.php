<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateAgentTemplatesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('agent_templates', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('business_type_id');
            $table->string('name');
            $table->string('agent_type'); // customer_service, booking, inventory, communication, analytics, payment
            $table->text('description');
            $table->string('icon')->nullable();
            $table->json('default_config')->nullable(); // Default configuration for this agent type
            $table->json('capabilities')->nullable(); // What this agent can do
            $table->json('triggers')->nullable(); // When this agent should activate
            $table->json('responses')->nullable(); // Default responses/templates
            $table->boolean('is_required')->default(false); // Must be enabled for this business type
            $table->boolean('is_active')->default(true);
            $table->integer('sort_order')->default(0);
            $table->timestamps();

            $table->foreign('business_type_id')->references('id')->on('business_types');
            $table->index(['business_type_id', 'agent_type']);
            $table->index(['is_active', 'sort_order']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('agent_templates');
    }
}
