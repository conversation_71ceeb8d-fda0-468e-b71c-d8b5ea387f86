<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ai_agents', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('type'); // 'openrouter', 'ollama', 'lmstudio'
            $table->string('model');
            $table->string('status')->default('stopped'); // 'running', 'stopped', 'error'
            $table->json('configuration')->nullable();
            $table->text('description')->nullable();
            $table->timestamp('last_activity')->nullable();
            $table->integer('total_requests')->default(0);
            $table->integer('successful_requests')->default(0);
            $table->integer('failed_requests')->default(0);
            $table->decimal('average_response_time', 8, 2)->nullable();
            $table->boolean('auto_start')->default(false);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ai_agents');
    }
};
