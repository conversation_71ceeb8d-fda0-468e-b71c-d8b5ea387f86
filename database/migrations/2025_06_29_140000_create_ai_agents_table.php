<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ai_agents', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('tenant_id');
            $table->unsignedBigInteger('agent_template_id')->nullable();
            $table->string('name');
            $table->string('agent_type'); // customer_service, booking, inventory, communication, analytics, payment
            $table->string('ai_provider'); // 'openrouter', 'ollama', 'lmstudio', 'openai', 'anthropic'
            $table->string('model');
            $table->string('status')->default('stopped'); // 'running', 'stopped', 'error', 'training'
            $table->json('configuration')->nullable();
            $table->json('capabilities')->nullable(); // What this agent can do
            $table->json('triggers')->nullable(); // When this agent should activate
            $table->json('responses')->nullable(); // Response templates
            $table->text('description')->nullable();
            $table->text('system_prompt')->nullable();
            $table->boolean('is_active')->default(true);
            $table->boolean('whatsapp_enabled')->default(false);
            $table->timestamp('last_activity')->nullable();
            $table->integer('total_requests')->default(0);
            $table->integer('successful_requests')->default(0);
            $table->integer('failed_requests')->default(0);
            $table->decimal('average_response_time', 8, 2)->nullable();
            $table->boolean('auto_start')->default(false);
            $table->timestamps();

            $table->foreign('tenant_id')->references('id')->on('tenants')->onDelete('cascade');
            $table->foreign('agent_template_id')->references('id')->on('agent_templates')->onDelete('set null');
            $table->index(['tenant_id', 'agent_type']);
            $table->index(['status', 'is_active']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ai_agents');
    }
};
