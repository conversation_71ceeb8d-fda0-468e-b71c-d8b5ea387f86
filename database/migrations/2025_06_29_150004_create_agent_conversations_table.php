<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateAgentConversationsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('agent_conversations', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('tenant_id');
            $table->unsignedBigInteger('ai_agent_id');
            $table->unsignedBigInteger('customer_id')->nullable();
            $table->string('whatsapp_chat_id')->nullable();
            $table->string('customer_phone')->nullable();
            $table->string('customer_name')->nullable();
            $table->enum('status', ['active', 'resolved', 'escalated', 'archived'])->default('active');
            $table->json('context')->nullable(); // Conversation context and variables
            $table->json('metadata')->nullable(); // Additional metadata
            $table->timestamp('last_message_at')->nullable();
            $table->timestamps();

            $table->foreign('tenant_id')->references('id')->on('tenants')->onDelete('cascade');
            $table->foreign('ai_agent_id')->references('id')->on('ai_agents')->onDelete('cascade');
            $table->foreign('customer_id')->references('id')->on('customers')->onDelete('set null');
            
            $table->index(['tenant_id', 'status']);
            $table->index(['whatsapp_chat_id', 'tenant_id']);
            $table->index(['customer_phone', 'tenant_id']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('agent_conversations');
    }
}
