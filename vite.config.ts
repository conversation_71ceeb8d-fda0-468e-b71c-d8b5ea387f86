import { defineConfig } from 'laravel-vite'
import vue from '@vitejs/plugin-vue'

export default defineConfig({
    server: {
        host: '0.0.0.0', // Toegankelijk vanaf andere apparaten
        port: 5173,
        strictPort: true,
        hmr: {
            host: 'localhost',
            port: 5173
        },
        watch: {
            usePolling: true, // Voor betere file watching
            interval: 100,
            ignored: ['**/.env/**', '**/node_modules/**']
        },
        cors: true
    },
    build: {
        rollupOptions: {
            output: {
                manualChunks: undefined
            }
        }
    },
    resolve: {
        alias: {
            "vue-i18n": "vue-i18n/dist/vue-i18n.cjs.js",
            "@": "/resources"
        }
    }
}).withPlugins(
    vue({
        template: {
            compilerOptions: {
                isCustomElement: (tag) => tag.includes('-')
            }
        }
    })
)
