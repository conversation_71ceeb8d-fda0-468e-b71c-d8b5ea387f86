name: CI

on: [push, pull_request]

jobs:
  build-test:
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        php: ['7.4', '8.0']

    name: PHP ${{ matrix.php }}

    steps:
    - name: Checkout
      uses: actions/checkout@v2

    - name: Install dependencies
      uses: shivammathur/setup-php@v2
      with:
        php-version: ${{ matrix.php }}
        extensions: exif

    - name: Install PHP 7 dependencies
      run: composer update --no-interaction --no-progress
      if: "matrix.php < 8"

    - name: Install PHP 8 dependencies
      run: composer update --ignore-platform-req=php --no-interaction --no-progress
      if: "matrix.php >= 8"

    - name: Check coding style
      run: ./vendor/bin/php-cs-fixer fix -v --dry-run --using-cache=no --config=.php-cs-fixer.dist.php

    - name: Unit Tests
      run: php ./vendor/bin/pest
