name: Build PR Image
on:
  pull_request:
    types: [opened,synchronize,reopened,closed]

jobs:

  build-application:
    name: Build and Push `application`
    runs-on: ubuntu-latest
    if: ${{ github.event_name != 'pull_request' || github.event.action != 'closed' }}
    outputs:
      tags: ${{ steps.meta.outputs.tags }}
    steps:
      - name: Checkout git repo
        uses: actions/checkout@v3
      - name: Generate UUID image name
        id: uuid
        run: echo "UUID_TAG_APP=$(uuidgen)" >> $GITHUB_ENV
      - name: Docker metadata
        id: meta
        uses: docker/metadata-action@v3
        with:
          images: registry.uffizzi.com/${{ env.UUID_TAG_APP }}
          tags: type=raw,value=60d
      - name: Build and Push Image to registry.uffizzi.com ephemeral registry
        uses: docker/build-push-action@v2
        with:
          push: true
          context: ./
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          file: ./uffizzi/Dockerfile

  build-nginx:
    needs: 
      - build-application
    name: Build and Push `nginx`
    runs-on: ubuntu-latest
    if: ${{ github.event_name != 'pull_request' || github.event.action != 'closed' }}
    outputs:
      tags: ${{ steps.meta.outputs.tags }}
    steps:
      - name: Checkout git repo
        uses: actions/checkout@v3
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2        
      - name: Generate UUID image name
        id: uuid
        run: echo "UUID_TAG_NGINX=$(uuidgen)" >> $GITHUB_ENV
      - name: Docker metadata
        id: meta
        uses: docker/metadata-action@v3
        with:
          images: registry.uffizzi.com/${{ env.UUID_TAG_NGINX }}
          tags: type=raw,value=60d
      - name: Build and Push Image to Uffizzi ephemeral registry
        uses: docker/build-push-action@v2
        with:
          push: true
          context: ./
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          file: ./uffizzi/nginx/Dockerfile
          build-args: |
            BASE_IMAGE=${{ needs.build-application.outputs.tags }}
          cache-from: type=gha
          cache-to: type=gha,mode=max


  build-crond:
    name: Build and Push `crond`
    runs-on: ubuntu-latest
    if: ${{ github.event_name != 'pull_request' || github.event.action != 'closed' }}
    outputs:
      tags: ${{ steps.meta.outputs.tags }}
    steps:
      - name: Checkout git repo
        uses: actions/checkout@v3
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2        
      - name: Generate UUID image name
        id: uuid
        run: echo "UUID_TAG_CROND=$(uuidgen)" >> $GITHUB_ENV
      - name: Docker metadata
        id: meta
        uses: docker/metadata-action@v3
        with:
          images: registry.uffizzi.com/${{ env.UUID_TAG_CROND }}
          tags: type=raw,value=60d
      - name: Build and Push Image to registry.uffizzi.com ephemeral registry
        uses: docker/build-push-action@v2
        with:
          push: true
          context: ./
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          file: ./uffizzi/crond/Dockerfile      
          cache-from: type=gha
          cache-to: type=gha,mode=max



  render-compose-file:
    name: Render Docker Compose File
    # Pass output of this workflow to another triggered by `workflow_run` event.
    runs-on: ubuntu-latest
    outputs:
      compose-file-cache-key: ${{ steps.hash.outputs.hash }}
    needs: 
      - build-application
      - build-nginx
      - build-crond
    steps:
      - name: Checkout git repo
        uses: actions/checkout@v3
      - name: Render Compose File
        run: |
          APP_IMAGE=$(echo ${{ needs.build-application.outputs.tags }})
          export APP_IMAGE
          NGINX_IMAGE=$(echo ${{ needs.build-nginx.outputs.tags }})
          export NGINX_IMAGE
          CROND_IMAGE=$(echo ${{ needs.build-crond.outputs.tags }})
          export CROND_IMAGE
          # Render simple template from environment variables.
          envsubst < ./uffizzi/docker-compose.uffizzi.yml > docker-compose.rendered.yml
          cat docker-compose.rendered.yml
      - name: Upload Rendered Compose File as Artifact
        uses: actions/upload-artifact@v3
        with:
          name: preview-spec
          path: docker-compose.rendered.yml
          retention-days: 2
      - name: Serialize PR Event to File
        run:  |
          cat << EOF > event.json
          ${{ toJSON(github.event) }} 
          
          EOF
      - name: Upload PR Event as Artifact
        uses: actions/upload-artifact@v3
        with:
          name: preview-spec
          path: event.json
          retention-days: 2

  delete-preview:
    name: Call for Preview Deletion
    runs-on: ubuntu-latest
    if: ${{ github.event.action == 'closed' }}
    steps:
      # If this PR is closing, we will not render a compose file nor pass it to the next workflow.
      - name: Serialize PR Event to File
        run: echo '${{ toJSON(github.event) }}' > event.json
      - name: Upload PR Event as Artifact
        uses: actions/upload-artifact@v3
        with:
          name: preview-spec
          path: event.json
          retention-days: 2

