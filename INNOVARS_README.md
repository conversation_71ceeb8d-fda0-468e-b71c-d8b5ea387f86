# 🤖 Innovars AI System CRM\n\n> Transform your business with intelligent customer relationship management powered by AI\n\n![Innovars AI CRM](resources/static/img/innovars-logo.svg)\n\n## ✨ Features\n\n### 🎯 Core CRM Features\n- **Customer Management** - Comprehensive customer profiles and history\n- **Invoice Generation** - Professional invoices with customizable templates\n- **Payment Tracking** - Monitor payments and outstanding balances\n- **Expense Management** - Track business expenses and categorize costs\n- **Reporting & Analytics** - Detailed financial reports and insights\n- **Multi-Company Support** - Manage multiple businesses from one dashboard\n\n### 🤖 AI-Powered Features\n- **Intelligent Insights** - AI-driven customer behavior analysis\n- **Revenue Predictions** - Smart forecasting based on historical data\n- **Automated Workflows** - AI-assisted task automation\n- **Smart Recommendations** - Personalized business suggestions\n- **Predictive Analytics** - Anticipate customer needs and trends\n\n### 🎨 Modern Interface\n- **Beautiful Design** - Modern, clean, and intuitive interface\n- **Responsive Layout** - Works perfectly on desktop, tablet, and mobile\n- **Dark/Light Mode** - Automatic theme switching based on preferences\n- **Gradient Themes** - Beautiful blue-to-purple gradient design\n- **Glass Morphism** - Modern UI effects and animations\n\n## 🚀 Quick Start\n\n### Prerequisites\n- Docker & Docker Compose\n- Node.js & npm (for frontend assets)\n- Git\n\n### Installation\n\n1. **Clone the repository**\n   ```bash\n   git clone <your-repo-url>\n   cd crater\n   ```\n\n2. **Start Innovars AI CRM**\n   ```bash\n   ./start-innovars.sh\n   ```\n\n3. **Access your CRM**\n   - Main Application: http://localhost\n   - Admin Panel: http://localhost/admin\n   - Database: localhost:33006\n\n## 🛠️ Development\n\n### Manual Setup\n\n1. **Environment Setup**\n   ```bash\n   cp .env.example .env\n   # Edit .env with your settings\n   ```\n\n2. **Start Services**\n   ```bash\n   docker-compose up -d\n   ```\n\n3. **Install Dependencies**\n   ```bash\n   docker-compose exec app composer install\n   npm install\n   ```\n\n4. **Database Setup**\n   ```bash\n   docker-compose exec app php artisan migrate\n   docker-compose exec app php artisan db:seed\n   ```\n\n5. **Build Assets**\n   ```bash\n   npm run dev\n   # or for production\n   npm run build\n   ```\n\n### Development Commands\n\n```bash\n# View logs\ndocker-compose logs -f\n\n# Access container shell\ndocker-compose exec app bash\n\n# Run artisan commands\ndocker-compose exec app php artisan <command>\n\n# Watch frontend changes\nnpm run dev\n\n# Run tests\ndocker-compose exec app php artisan test\n```\n\n## 🎨 Customization\n\n### Branding\n- Logo: `resources/static/img/innovars-logo.svg`\n- Colors: `resources/sass/themes.scss`\n- Tailwind Config: `tailwind.config.js`\n\n### AI Features\n- AI Insights: `resources/scripts/admin/views/dashboard/InnovarsAIDashboard.vue`\n- AI Assistant: Configure in `.env` file\n\n### Themes\nThe system uses a modern gradient-based color palette:\n- **Primary**: Blue gradient (#0EA5E9 to #0284C7)\n- **Secondary**: Purple gradient (#8B5CF6 to #7C3AED)\n- **Success**: Green (#22C55E)\n- **Warning**: Orange (#F59E0B)\n- **Error**: Red (#EF4444)\n\n## 📱 Components\n\n### New Components Created\n- `InnovarsAIDashboard.vue` - AI-powered dashboard\n- `InnovarsHeader.vue` - Modern header with AI assistant\n- `InnovarsSidebar.vue` - Gradient sidebar with AI features\n- `InnovarsLogin.vue` - Split-screen login with hero section\n\n### Styling\n- Modern card designs with hover effects\n- Glass morphism effects\n- Smooth animations and transitions\n- Responsive grid layouts\n- Custom gradient backgrounds\n\n## 🔧 Configuration\n\n### Environment Variables\n```env\n# Innovars AI Branding\nAPP_NAME=\"Innovars AI System CRM\"\nCOMPANY_NAME=\"Innovars AI System\"\nCOMPANY_LOGO=\"/img/innovars-logo.svg\"\nCOMPANY_LOGO_WHITE=\"/img/innovars-logo-white.svg\"\n\n# AI Features\nAI_ENABLED=true\nAI_PROVIDER=openai\nAI_API_KEY=your_api_key_here\n```\n\n### Database\n- **Engine**: MySQL/MariaDB\n- **Host**: db (Docker container)\n- **Port**: 3306 (internal), 33006 (external)\n- **Database**: crater\n- **Username**: crater\n- **Password**: crater\n\n## 🚀 Deployment\n\n### Production Deployment\n\n1. **Environment Setup**\n   ```bash\n   cp .env.example .env.production\n   # Configure production settings\n   ```\n\n2. **Build for Production**\n   ```bash\n   npm run build\n   docker-compose -f docker-compose.prod.yml up -d\n   ```\n\n3. **Optimize Application**\n   ```bash\n   docker-compose exec app php artisan config:cache\n   docker-compose exec app php artisan route:cache\n   docker-compose exec app php artisan view:cache\n   ```\n\n### Performance Optimization\n- Enable Redis for caching\n- Configure CDN for static assets\n- Set up SSL certificates\n- Enable gzip compression\n- Configure database indexing\n\n## 🔒 Security\n\n- CSRF protection enabled\n- SQL injection prevention\n- XSS protection\n- Secure authentication\n- Role-based permissions\n- API rate limiting\n\n## 📊 Monitoring\n\n### Health Checks\n```bash\n# Check application status\ncurl http://localhost/health\n\n# Check database connection\ndocker-compose exec app php artisan tinker\n>>> DB::connection()->getPdo();\n```\n\n### Logs\n- Application logs: `storage/logs/laravel.log`\n- Web server logs: `docker-compose logs nginx`\n- Database logs: `docker-compose logs db`\n\n## 🤝 Contributing\n\n1. Fork the repository\n2. Create a feature branch\n3. Make your changes\n4. Add tests if applicable\n5. Submit a pull request\n\n## 📄 License\n\nThis project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.\n\n## 🆘 Support\n\n### Common Issues\n\n**Docker containers won't start**\n```bash\n# Check Docker status\ndocker info\n\n# Rebuild containers\ndocker-compose down\ndocker-compose up -d --build\n```\n\n**Frontend assets not loading**\n```bash\n# Rebuild assets\nnpm run build\n\n# Clear cache\ndocker-compose exec app php artisan cache:clear\n```\n\n**Database connection issues**\n```bash\n# Check database container\ndocker-compose logs db\n\n# Reset database\ndocker-compose exec app php artisan migrate:fresh\n```\n\n### Getting Help\n- Check the logs: `docker-compose logs -f`\n- Review the documentation\n- Check GitHub issues\n- Contact support\n\n---\n\n**Built with ❤️ by Innovars AI Team**\n\n*Transform your business with intelligent CRM solutions*