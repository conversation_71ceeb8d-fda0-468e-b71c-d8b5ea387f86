<!DOCTYPE html>
<html lang="nl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Innovars CRM Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: Arial, sans-serif;
            background-color: #f4f4f4;
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        /* Header */
        .header {
            background-color: #ff7f50;
            color: white;
            text-align: center;
            padding: 30px 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .header h1 {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        /* Main Content */
        .main-content {
            display: flex;
            flex-direction: column;
            gap: 30px;
        }

        .section {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }

        .section:hover {
            transform: translateY(-2px);
        }

        .section h2 {
            color: #ff7f50;
            font-size: 1.8rem;
            margin-bottom: 15px;
            border-bottom: 2px solid #ff7f50;
            padding-bottom: 10px;
        }

        .section p {
            margin-bottom: 20px;
            color: #666;
            font-size: 1rem;
        }

        /* Button Grid */
        .button-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .btn {
            background-color: #ff7f50;
            color: white;
            border: none;
            padding: 15px 20px;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
            text-decoration: none;
            display: inline-block;
        }

        .btn:hover {
            background-color: #ff6347;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }

        .btn:active {
            transform: translateY(0);
        }

        /* Footer */
        .footer {
            text-align: center;
            margin-top: 40px;
            padding: 20px;
            color: #666;
            font-size: 0.9rem;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }
            
            .section {
                padding: 20px;
            }
            
            .button-grid {
                grid-template-columns: 1fr;
            }
        }

        /* Status Indicators */
        .status {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: bold;
            margin-left: 10px;
        }

        .status.active {
            background-color: #4CAF50;
            color: white;
        }

        .status.inactive {
            background-color: #f44336;
            color: white;
        }

        .status.pending {
            background-color: #ff9800;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="header">
            <h1>Innovars CRM Dashboard</h1>
            <p>Beheer je klanten, taken en API configuraties op één plek</p>
        </header>

        <!-- Main Content -->
        <main class="main-content">
            <!-- AI Agents Section -->
            <section class="section">
                <h2>🤖 AI Agents <span class="status active">2 Actief</span></h2>
                <p>Beheer je AI-agents voor geautomatiseerde klantinteracties en data-analyse.</p>
                <div class="button-grid">
                    <button class="btn" onclick="startAgent()">Agent Starten</button>
                    <button class="btn" onclick="stopAllAgents()">Alle Agents Stoppen</button>
                    <button class="btn" onclick="viewAgentStatus()">Agent Status</button>
                    <button class="btn" onclick="configureAgent()">Agent Configureren</button>
                </div>
            </section>

            <!-- Task Scheduler Section -->
            <section class="section">
                <h2>📅 Task Scheduler <span class="status pending">5 Taken</span></h2>
                <p>Plan en beheer geautomatiseerde taken voor je CRM-systeem.</p>
                <div class="button-grid">
                    <button class="btn" onclick="createTask()">Nieuwe Taak Maken</button>
                    <button class="btn" onclick="viewHistory()">Geschiedenis Bekijken</button>
                    <button class="btn" onclick="manageTasks()">Taken Beheren</button>
                    <button class="btn" onclick="scheduleReport()">Rapport Plannen</button>
                </div>
            </section>

            <!-- API Configuration Section -->
            <section class="section">
                <h2>🔧 API Configuration <span class="status active">Verbonden</span></h2>
                <p>Configureer en beheer je API-verbindingen en beveiligingsinstellingen.</p>
                <div class="button-grid">
                    <button class="btn" onclick="configureAPI()">API Configureren</button>
                    <button class="btn" onclick="testAPI()">API Testen</button>
                    <button class="btn" onclick="rotateKeys()">Sleutels Roteren</button>
                    <button class="btn" onclick="viewLogs()">API Logs Bekijken</button>
                </div>
            </section>

            <!-- Customer Management Section -->
            <section class="section">
                <h2>👥 Klantenbeheer <span class="status active">1,247 Klanten</span></h2>
                <p>Beheer je klantendatabase en analyseer klantgedrag.</p>
                <div class="button-grid">
                    <button class="btn" onclick="addCustomer()">Klant Toevoegen</button>
                    <button class="btn" onclick="viewCustomers()">Klanten Bekijken</button>
                    <button class="btn" onclick="customerAnalytics()">Klant Analytics</button>
                    <button class="btn" onclick="exportData()">Data Exporteren</button>
                </div>
            </section>

            <!-- Reports & Analytics Section -->
            <section class="section">
                <h2>📊 Rapporten & Analytics <span class="status active">Live Data</span></h2>
                <p>Genereer rapporten en bekijk real-time analytics van je CRM-data.</p>
                <div class="button-grid">
                    <button class="btn" onclick="generateReport()">Rapport Genereren</button>
                    <button class="btn" onclick="viewDashboard()">Analytics Dashboard</button>
                    <button class="btn" onclick="salesReport()">Verkoop Rapport</button>
                    <button class="btn" onclick="customReport()">Custom Rapport</button>
                </div>
            </section>
        </main>

        <!-- Footer -->
        <footer class="footer">
            <p>© 2025 Innovars Labs. Alle rechten voorbehouden.</p>
        </footer>
    </div>

    <script>
        // AI Agents Functions
        function startAgent() {
            alert('AI Agent wordt gestart...');
            console.log('Starting AI Agent');
        }

        function stopAllAgents() {
            if(confirm('Weet je zeker dat je alle agents wilt stoppen?')) {
                alert('Alle AI Agents zijn gestopt.');
                console.log('All agents stopped');
            }
        }

        function viewAgentStatus() {
            alert('Agent Status: 2 actief, 0 inactief');
            console.log('Viewing agent status');
        }

        function configureAgent() {
            alert('Agent configuratie wordt geopend...');
            console.log('Opening agent configuration');
        }

        // Task Scheduler Functions
        function createTask() {
            alert('Nieuwe taak maken interface wordt geopend...');
            console.log('Creating new task');
        }

        function viewHistory() {
            alert('Taak geschiedenis wordt geladen...');
            console.log('Loading task history');
        }

        function manageTasks() {
            alert('Taak beheer interface wordt geopend...');
            console.log('Opening task management');
        }

        function scheduleReport() {
            alert('Rapport planning interface wordt geopend...');
            console.log('Opening report scheduler');
        }

        // API Configuration Functions
        function configureAPI() {
            alert('API configuratie wordt geopend...');
            console.log('Opening API configuration');
        }

        function testAPI() {
            alert('API verbinding wordt getest...');
            setTimeout(() => {
                alert('API test succesvol! Verbinding is actief.');
            }, 1000);
        }

        function rotateKeys() {
            if(confirm('Weet je zeker dat je de API sleutels wilt roteren?')) {
                alert('API sleutels zijn succesvol geroteerd.');
                console.log('API keys rotated');
            }
        }

        function viewLogs() {
            alert('API logs worden geladen...');
            console.log('Loading API logs');
        }

        // Customer Management Functions
        function addCustomer() {
            alert('Nieuwe klant toevoegen interface wordt geopend...');
            console.log('Opening add customer form');
        }

        function viewCustomers() {
            alert('Klanten overzicht wordt geladen...');
            console.log('Loading customer overview');
        }

        function customerAnalytics() {
            alert('Klant analytics dashboard wordt geopend...');
            console.log('Opening customer analytics');
        }

        function exportData() {
            alert('Data export wordt voorbereid...');
            console.log('Preparing data export');
        }

        // Reports & Analytics Functions
        function generateReport() {
            alert('Rapport wordt gegenereerd...');
            console.log('Generating report');
        }

        function viewDashboard() {
            alert('Analytics dashboard wordt geopend...');
            console.log('Opening analytics dashboard');
        }

        function salesReport() {
            alert('Verkoop rapport wordt geladen...');
            console.log('Loading sales report');
        }

        function customReport() {
            alert('Custom rapport builder wordt geopend...');
            console.log('Opening custom report builder');
        }

        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Innovars CRM Dashboard geladen');
            
            // Simulate real-time updates
            setInterval(function() {
                // Update status indicators or refresh data
                console.log('Dashboard data refreshed');
            }, 30000); // Every 30 seconds
        });
    </script>
</body>
</html>