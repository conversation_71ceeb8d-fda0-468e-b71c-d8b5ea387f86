#!/bin/bash

# Innovars AI System CRM Startup Script
echo "🚀 Starting Innovars AI System CRM..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    print_error "Docker is not running. Please start Docker first."
    exit 1
fi

print_status "Docker is running ✓"

# Stop existing containers
print_status "Stopping existing containers..."
docker-compose down

# Build and start containers
print_status "Building and starting Innovars AI System CRM containers..."
docker-compose up -d --build

# Wait for containers to be ready
print_status "Waiting for containers to be ready..."
sleep 10

# Check if containers are running
if docker-compose ps | grep -q "Up"; then
    print_success "Containers are running!"
else
    print_error "Failed to start containers"
    docker-compose logs
    exit 1
fi

# Install dependencies if needed
print_status "Installing PHP dependencies..."
docker-compose exec app composer install --no-dev --optimize-autoloader

# Generate application key if needed
print_status "Setting up application..."
docker-compose exec app php artisan key:generate --force

# Run migrations
print_status "Running database migrations..."
docker-compose exec app php artisan migrate --force

# Clear caches
print_status "Clearing caches..."
docker-compose exec app php artisan config:clear
docker-compose exec app php artisan cache:clear
docker-compose exec app php artisan view:clear

# Install and build frontend assets
print_status "Installing and building frontend assets..."
if command -v npm &> /dev/null; then
    npm install
    npm run build
else
    print_warning "npm not found. Please install Node.js and npm to build frontend assets."
fi

# Set permissions
print_status "Setting permissions..."
docker-compose exec app chown -R www-data:www-data /var/www/storage
docker-compose exec app chown -R www-data:www-data /var/www/bootstrap/cache

print_success "🎉 Innovars AI System CRM is ready!"
echo ""
echo "📱 Access your CRM at: http://localhost"
echo "🗄️  Database: localhost:33006"
echo "📊 Admin Panel: http://localhost/admin"
echo ""
echo "🤖 AI Features:"
echo "   • Intelligent customer insights"
echo "   • Automated invoice generation"
echo "   • Smart financial forecasting"
echo "   • AI-powered analytics"
echo ""
echo "🔧 Useful commands:"
echo "   • View logs: docker-compose logs -f"
echo "   • Stop: docker-compose down"
echo "   • Restart: docker-compose restart"
echo ""
print_success "Happy CRM-ing with Innovars AI! 🚀"