{
    "folders": [
        {
            "path": "."
        }
    ],
    "settings": {
        // AI & Copilot Configuration
        "github.copilot.enable": {
            "*": true,
            "yaml": false,
            "plaintext": false,
            "markdown": false
        },
        "continue.telemetryEnabled": false,
        "editor.inlineSuggest.enabled": true,
        "editor.suggest.preview": true,
        "editor.acceptSuggestionOnCommitCharacter": false,
        "editor.acceptSuggestionOnEnter": "on",

        // Existing settings
        "search.exclude": {
            "**/public": true
        },
        "editor.formatOnSave": true,
        "vetur.validation.template": false,
        "editor.codeActionsOnSave": {
            "source.fixAll.eslint": "explicit"
        },
        "editor.formatOnPaste": true,
        "editor.formatOnType": true,
        "editor.codeActionsOnSaveTimeout": 2000,
        "prettier.semi": false,
        "prettier.singleQuote": true,
        "files.associations": {},
        "eslint.codeAction.disableRuleComment": {},
        "eslint.codeAction.showDocumentation": {
            "enable": true
        },
        "eslint.validate": [
            "javascript",
            "javascriptreact",
            "vue"
        ],
        "[php]": {
            "editor.defaultFormatter": "junstyle.php-cs-fixer"
        },
        "debug.allowBreakpointsEverywhere": true,
        "files.autoGuessEncoding": true,
        "files.exclude": {
            "**/.vscode": false,
            "compile_commands.json": true,
            "*.hrccproj": true,
            "*.sln": true,
            "*.suo": true
        }
    },
    "extensions": {
        "recommendations": [
            "continue.continue",
            "github.copilot",
            "github.copilot-chat",
            "ms-vscode.vscode-typescript-next",
            "vue.volar",
            "junstyle.php-cs-fixer"
        ]
    }
}