#!/bin/bash

# Innovars AI Agents System - Global VS Code Setup
# Dit script configureert VS Code globaal voor gebruik met OpenRouter, Ollama en LM Studio

set -e

echo "🚀 Innovars AI Agents System - Global Setup"
echo "============================================"

# Kleuren voor output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# VS Code configuratie directory
VSCODE_USER_DIR="$HOME/Library/Application Support/Code/User"
CONTINUE_CONFIG_DIR="$HOME/.continue"

echo -e "${BLUE}📁 VS Code configuratie directory: $VSCODE_USER_DIR${NC}"

# Backup maken van bestaande configuratie
echo -e "${YELLOW}📋 Backup maken van bestaande configuratie...${NC}"
if [ -f "$VSCODE_USER_DIR/settings.json" ]; then
    cp "$VSCODE_USER_DIR/settings.json" "$VSCODE_USER_DIR/settings.json.backup-$(date +%Y%m%d-%H%M%S)"
    echo -e "${GREEN}✅ Backup gemaakt${NC}"
fi

# Continue.dev configuratie directory maken
echo -e "${YELLOW}📁 Continue.dev configuratie directory maken...${NC}"
mkdir -p "$CONTINUE_CONFIG_DIR"

# Continue.dev globale configuratie
echo -e "${YELLOW}⚙️  Continue.dev globale configuratie maken...${NC}"
cat > "$CONTINUE_CONFIG_DIR/config.json" << 'EOF'
{
  "models": [
    {
      "title": "OpenRouter - Claude 3.5 Sonnet",
      "provider": "openrouter",
      "model": "anthropic/claude-3.5-sonnet",
      "apiKey": "${OPENROUTER_API_KEY}",
      "apiBase": "https://openrouter.ai/api/v1"
    },
    {
      "title": "OpenRouter - GPT-4o",
      "provider": "openrouter", 
      "model": "openai/gpt-4o",
      "apiKey": "${OPENROUTER_API_KEY}",
      "apiBase": "https://openrouter.ai/api/v1"
    },
    {
      "title": "OpenRouter - DeepSeek Coder",
      "provider": "openrouter",
      "model": "deepseek/deepseek-coder",
      "apiKey": "${OPENROUTER_API_KEY}",
      "apiBase": "https://openrouter.ai/api/v1"
    },
    {
      "title": "Ollama - CodeLlama",
      "provider": "ollama",
      "model": "codellama:13b",
      "apiBase": "http://localhost:11434"
    },
    {
      "title": "Ollama - DeepSeek Coder",
      "provider": "ollama",
      "model": "deepseek-coder:6.7b",
      "apiBase": "http://localhost:11434"
    },
    {
      "title": "Ollama - Llama 3.1",
      "provider": "ollama",
      "model": "llama3.1:8b",
      "apiBase": "http://localhost:11434"
    },
    {
      "title": "LM Studio",
      "provider": "lmstudio",
      "model": "local-model",
      "apiBase": "http://localhost:1234/v1"
    }
  ],
  "tabAutocompleteModel": {
    "title": "Ollama - CodeLlama Autocomplete",
    "provider": "ollama",
    "model": "codellama:7b",
    "apiBase": "http://localhost:11434"
  },
  "customCommands": [
    {
      "name": "test",
      "prompt": "Write comprehensive tests for the following code. Include unit tests, integration tests, and edge cases. Use the appropriate testing framework:\n\n{{{ input }}}"
    },
    {
      "name": "docs",
      "prompt": "Write comprehensive documentation for the following code. Include parameter descriptions, return values, examples:\n\n{{{ input }}}"
    },
    {
      "name": "optimize",
      "prompt": "Analyze and optimize this code for performance, readability, and maintainability:\n\n{{{ input }}}"
    },
    {
      "name": "explain",
      "prompt": "Explain how this code works step by step:\n\n{{{ input }}}"
    },
    {
      "name": "refactor",
      "prompt": "Refactor this code following best practices and design patterns:\n\n{{{ input }}}"
    },
    {
      "name": "security",
      "prompt": "Analyze this code for security vulnerabilities and suggest improvements:\n\n{{{ input }}}"
    }
  ],
  "contextProviders": [
    {
      "name": "diff",
      "params": {}
    },
    {
      "name": "folder",
      "params": {
        "folders": ["src", "app", "lib", "components", "utils", "config"]
      }
    },
    {
      "name": "codebase",
      "params": {}
    },
    {
      "name": "git",
      "params": {}
    }
  ],
  "slashCommands": [
    {
      "name": "edit",
      "description": "Edit selected code"
    },
    {
      "name": "comment",
      "description": "Write comments for the selected code"
    },
    {
      "name": "share",
      "description": "Export the current chat session"
    },
    {
      "name": "cmd",
      "description": "Generate a shell command"
    }
  ],
  "allowAnonymousTelemetry": false,
  "embeddingsProvider": {
    "provider": "ollama",
    "model": "nomic-embed-text",
    "apiBase": "http://localhost:11434"
  }
}
EOF

echo -e "${GREEN}✅ Continue.dev configuratie aangemaakt${NC}"

# Environment bestand voor API keys
echo -e "${YELLOW}🔑 Environment bestand maken...${NC}"
cat > "$HOME/.ai-config" << 'EOF'
# Innovars AI Agents System - Global Configuration
# Vul je eigen API keys in

# OpenRouter API Key
# Verkrijg van: https://openrouter.ai/keys
export OPENROUTER_API_KEY="your_openrouter_api_key_here"

# Ollama Configuration
export OLLAMA_BASE_URL="http://localhost:11434"

# LM Studio Configuration  
export LMSTUDIO_BASE_URL="http://localhost:1234/v1"

# Continue.dev Settings
export CONTINUE_TELEMETRY=false
EOF

echo -e "${GREEN}✅ Environment bestand aangemaakt: $HOME/.ai-config${NC}"

# VS Code extensions installeren
echo -e "${YELLOW}🔌 VS Code extensions installeren...${NC}"
extensions=(
    "continue.continue"
    "github.copilot"
    "github.copilot-chat"
    "ms-vscode.vscode-typescript-next"
    "bradlc.vscode-tailwindcss"
    "esbenp.prettier-vscode"
    "ms-python.python"
    "ms-python.black-formatter"
)

for ext in "${extensions[@]}"; do
    echo -e "${BLUE}  Installing $ext...${NC}"
    code --install-extension "$ext" --force
done

echo -e "${GREEN}✅ Extensions geïnstalleerd${NC}"

# Ollama modellen downloaden (optioneel)
echo -e "${YELLOW}🤖 Wil je Ollama modellen downloaden? (y/n)${NC}"
read -r download_models

if [[ $download_models =~ ^[Yy]$ ]]; then
    echo -e "${BLUE}📥 Ollama modellen downloaden...${NC}"
    
    # Check of Ollama geïnstalleerd is
    if command -v ollama &> /dev/null; then
        ollama pull codellama:7b
        ollama pull codellama:13b
        ollama pull deepseek-coder:6.7b
        ollama pull llama3.1:8b
        ollama pull nomic-embed-text
        echo -e "${GREEN}✅ Ollama modellen gedownload${NC}"
    else
        echo -e "${RED}❌ Ollama niet gevonden. Installeer eerst Ollama: https://ollama.ai${NC}"
    fi
fi

echo ""
echo -e "${GREEN}🎉 Setup voltooid!${NC}"
echo ""
echo -e "${BLUE}📋 Volgende stappen:${NC}"
echo -e "1. Bewerk $HOME/.ai-config en vul je OpenRouter API key in"
echo -e "2. Herstart VS Code"
echo -e "3. Open Continue.dev met Ctrl+Shift+I"
echo -e "4. Selecteer een model en begin met coderen!"
echo ""
echo -e "${YELLOW}💡 Tips:${NC}"
echo -e "• Gebruik Ctrl+I voor inline editing"
echo -e "• Gebruik Ctrl+Shift+I voor chat interface"
echo -e "• Gebruik /test, /docs, /optimize voor custom commands"
echo -e "• Switch tussen modellen in Continue.dev interface"
echo ""
echo -e "${BLUE}🔗 Nuttige links:${NC}"
echo -e "• OpenRouter: https://openrouter.ai"
echo -e "• Ollama: https://ollama.ai"
echo -e "• LM Studio: https://lmstudio.ai"
echo -e "• Continue.dev: https://continue.dev"
